{"ast": null, "code": "\"use strict\";\n\nrequire(\"core-js/modules/es.string.includes.js\");\nvar _interopRequireDefault = require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = exports.ApprovalManager = exports.APPROVAL_STATUS_TEXT = exports.APPROVAL_STATUS = exports.APPROVAL_FLOW = void 0;\nrequire(\"core-js/modules/es.error.cause.js\");\nrequire(\"core-js/modules/es.array.includes.js\");\nvar _classCallCheck2 = _interopRequireDefault(require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/classCallCheck.js\"));\nvar _createClass2 = _interopRequireDefault(require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/createClass.js\"));\nvar _defineProperty2 = _interopRequireDefault(require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/defineProperty.js\"));\n/**\n * 审批流程状态管理系统\n * \n * 审批状态定义：\n * 0: 未审批\n * 1: 全部同意（最终完成状态）\n * 2: 已拒绝（最终拒绝状态）\n * 3: 贷后主管审批（当前审批节点）\n * 4: 总监审批（当前审批节点）\n * 5: 财务主管/总监抄送（当前审批节点）\n * 6: 总经理/董事长审批(抄送)（当前审批节点）\n */\n\n// 审批状态常量\nvar APPROVAL_STATUS = exports.APPROVAL_STATUS = {\n  PENDING: 0,\n  // 未审批\n  APPROVED: 1,\n  // 全部同意\n  REJECTED: 2,\n  // 已拒绝\n  LEGAL_SUPERVISOR: 3,\n  // 贷后主管审批\n  DIRECTOR: 4,\n  // 总监审批\n  FINANCE_SUPERVISOR: 5,\n  // 财务主管/总监抄送\n  GENERAL_MANAGER: 6 // 总经理/董事长审批\n};\n\n// 审批状态描述\nvar APPROVAL_STATUS_TEXT = exports.APPROVAL_STATUS_TEXT = (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({}, APPROVAL_STATUS.PENDING, '未审批'), APPROVAL_STATUS.APPROVED, '全部同意'), APPROVAL_STATUS.REJECTED, '已拒绝'), APPROVAL_STATUS.LEGAL_SUPERVISOR, '贷后主管审批'), APPROVAL_STATUS.DIRECTOR, '总监审批'), APPROVAL_STATUS.FINANCE_SUPERVISOR, '财务主管/总监抄送'), APPROVAL_STATUS.GENERAL_MANAGER, '总经理/董事长审批');\n\n// 审批流程顺序\nvar APPROVAL_FLOW = exports.APPROVAL_FLOW = [APPROVAL_STATUS.LEGAL_SUPERVISOR,\n// 贷后主管审批\nAPPROVAL_STATUS.DIRECTOR,\n// 总监审批\nAPPROVAL_STATUS.FINANCE_SUPERVISOR,\n// 财务主管/总监抄送\nAPPROVAL_STATUS.GENERAL_MANAGER // 总经理/董事长审批\n];\n\n/**\n * 审批流程管理类\n */\nvar ApprovalManager = exports.ApprovalManager = /*#__PURE__*/function () {\n  function ApprovalManager() {\n    (0, _classCallCheck2.default)(this, ApprovalManager);\n  }\n  return (0, _createClass2.default)(ApprovalManager, null, [{\n    key: \"getNextStatus\",\n    value:\n    /**\n     * 获取下一个审批状态\n     * @param {number} currentStatus 当前状态\n     * @returns {number|null} 下一个状态，如果没有下一个状态返回null\n     */\n    function getNextStatus(currentStatus) {\n      var currentIndex = APPROVAL_FLOW.indexOf(currentStatus);\n      if (currentIndex === -1 || currentIndex === APPROVAL_FLOW.length - 1) {\n        return null;\n      }\n      return APPROVAL_FLOW[currentIndex + 1];\n    }\n\n    /**\n     * 检查是否为最后一个审批节点\n     * @param {number} status 当前状态\n     * @returns {boolean}\n     */\n  }, {\n    key: \"isLastApprovalNode\",\n    value: function isLastApprovalNode(status) {\n      return status === APPROVAL_STATUS.GENERAL_MANAGER;\n    }\n\n    /**\n     * 检查是否为最终状态（已完成或已拒绝）\n     * @param {number} status 状态\n     * @returns {boolean}\n     */\n  }, {\n    key: \"isFinalStatus\",\n    value: function isFinalStatus(status) {\n      return status === APPROVAL_STATUS.APPROVED || status === APPROVAL_STATUS.REJECTED;\n    }\n\n    /**\n     * 检查是否可以进行审批操作\n     * @param {number} status 当前状态\n     * @returns {boolean}\n     */\n  }, {\n    key: \"canApprove\",\n    value: function canApprove(status) {\n      return APPROVAL_FLOW.includes(status);\n    }\n\n    /**\n     * 处理审批通过\n     * @param {number} currentStatus 当前状态\n     * @returns {number} 新状态\n     */\n  }, {\n    key: \"handleApprove\",\n    value: function handleApprove(currentStatus) {\n      if (!this.canApprove(currentStatus)) {\n        throw new Error('当前状态不允许审批操作');\n      }\n\n      // 如果是最后一个审批节点，设置为全部同意\n      if (this.isLastApprovalNode(currentStatus)) {\n        return APPROVAL_STATUS.APPROVED;\n      }\n\n      // 否则进入下一个审批节点\n      var nextStatus = this.getNextStatus(currentStatus);\n      if (nextStatus === null) {\n        throw new Error('无法获取下一个审批状态');\n      }\n      return nextStatus;\n    }\n\n    /**\n     * 处理审批拒绝\n     * @param {number} currentStatus 当前状态\n     * @returns {number} 新状态（始终为已拒绝）\n     */\n  }, {\n    key: \"handleReject\",\n    value: function handleReject(currentStatus) {\n      if (!this.canApprove(currentStatus)) {\n        throw new Error('当前状态不允许审批操作');\n      }\n      return APPROVAL_STATUS.REJECTED;\n    }\n\n    /**\n     * 开始审批流程\n     * @returns {number} 第一个审批节点状态\n     */\n  }, {\n    key: \"startApprovalFlow\",\n    value: function startApprovalFlow() {\n      return APPROVAL_FLOW[0];\n    }\n\n    /**\n     * 获取状态描述文本\n     * @param {number} status 状态码\n     * @returns {string} 状态描述\n     */\n  }, {\n    key: \"getStatusText\",\n    value: function getStatusText(status) {\n      return APPROVAL_STATUS_TEXT[status] || '未知状态';\n    }\n\n    /**\n     * 获取状态对应的标签类型（用于UI显示）\n     * @param {number} status 状态码\n     * @returns {string} 标签类型\n     */\n  }, {\n    key: \"getStatusTagType\",\n    value: function getStatusTagType(status) {\n      switch (status) {\n        case APPROVAL_STATUS.PENDING:\n          return 'info';\n        case APPROVAL_STATUS.APPROVED:\n          return 'success';\n        case APPROVAL_STATUS.REJECTED:\n          return 'danger';\n        case APPROVAL_STATUS.LEGAL_SUPERVISOR:\n        case APPROVAL_STATUS.DIRECTOR:\n        case APPROVAL_STATUS.FINANCE_SUPERVISOR:\n        case APPROVAL_STATUS.GENERAL_MANAGER:\n          return 'warning';\n        default:\n          return 'info';\n      }\n    }\n\n    /**\n     * 验证状态转换是否合法\n     * @param {number} fromStatus 原状态\n     * @param {number} toStatus 目标状态\n     * @returns {boolean} 是否合法\n     */\n  }, {\n    key: \"isValidStatusTransition\",\n    value: function isValidStatusTransition(fromStatus, toStatus) {\n      // 已完成或已拒绝的状态不能再转换\n      if (this.isFinalStatus(fromStatus)) {\n        return false;\n      }\n\n      // 只能转换为拒绝状态或下一个审批节点\n      if (toStatus === APPROVAL_STATUS.REJECTED) {\n        return this.canApprove(fromStatus);\n      }\n\n      // 检查是否为合法的下一个状态\n      if (this.isLastApprovalNode(fromStatus)) {\n        return toStatus === APPROVAL_STATUS.APPROVED;\n      }\n      return toStatus === this.getNextStatus(fromStatus);\n    }\n  }]);\n}();\nvar _default = exports.default = ApprovalManager;", "map": {"version": 3, "names": ["APPROVAL_STATUS", "exports", "PENDING", "APPROVED", "REJECTED", "LEGAL_SUPERVISOR", "DIRECTOR", "FINANCE_SUPERVISOR", "GENERAL_MANAGER", "APPROVAL_STATUS_TEXT", "_defineProperty2", "default", "APPROVAL_FLOW", "A<PERSON>rovalManager", "_classCallCheck2", "_createClass2", "key", "value", "getNextStatus", "currentStatus", "currentIndex", "indexOf", "length", "isLastApprovalNode", "status", "isFinalStatus", "canApprove", "includes", "handleApprove", "Error", "nextStatus", "handleReject", "startApprovalFlow", "getStatusText", "getStatusTagType", "isValidStatusTransition", "fromStatus", "to<PERSON><PERSON><PERSON>", "_default"], "sources": ["D:/code_project/java_project/loan/post-loan-backend-page/src/utils/approvalStatus.js"], "sourcesContent": ["/**\n * 审批流程状态管理系统\n * \n * 审批状态定义：\n * 0: 未审批\n * 1: 全部同意（最终完成状态）\n * 2: 已拒绝（最终拒绝状态）\n * 3: 贷后主管审批（当前审批节点）\n * 4: 总监审批（当前审批节点）\n * 5: 财务主管/总监抄送（当前审批节点）\n * 6: 总经理/董事长审批(抄送)（当前审批节点）\n */\n\n// 审批状态常量\nexport const APPROVAL_STATUS = {\n  PENDING: 0,           // 未审批\n  APPROVED: 1,          // 全部同意\n  REJECTED: 2,          // 已拒绝\n  LEGAL_SUPERVISOR: 3,  // 贷后主管审批\n  DIRECTOR: 4,          // 总监审批\n  FINANCE_SUPERVISOR: 5, // 财务主管/总监抄送\n  GENERAL_MANAGER: 6    // 总经理/董事长审批\n}\n\n// 审批状态描述\nexport const APPROVAL_STATUS_TEXT = {\n  [APPROVAL_STATUS.PENDING]: '未审批',\n  [APPROVAL_STATUS.APPROVED]: '全部同意',\n  [APPROVAL_STATUS.REJECTED]: '已拒绝',\n  [APPROVAL_STATUS.LEGAL_SUPERVISOR]: '贷后主管审批',\n  [APPROVAL_STATUS.DIRECTOR]: '总监审批',\n  [APPROVAL_STATUS.FINANCE_SUPERVISOR]: '财务主管/总监抄送',\n  [APPROVAL_STATUS.GENERAL_MANAGER]: '总经理/董事长审批'\n}\n\n// 审批流程顺序\nexport const APPROVAL_FLOW = [\n  APPROVAL_STATUS.LEGAL_SUPERVISOR,   // 贷后主管审批\n  APPROVAL_STATUS.DIRECTOR,           // 总监审批\n  APPROVAL_STATUS.FINANCE_SUPERVISOR, // 财务主管/总监抄送\n  APPROVAL_STATUS.GENERAL_MANAGER     // 总经理/董事长审批\n]\n\n/**\n * 审批流程管理类\n */\nexport class ApprovalManager {\n  \n  /**\n   * 获取下一个审批状态\n   * @param {number} currentStatus 当前状态\n   * @returns {number|null} 下一个状态，如果没有下一个状态返回null\n   */\n  static getNextStatus(currentStatus) {\n    const currentIndex = APPROVAL_FLOW.indexOf(currentStatus)\n    if (currentIndex === -1 || currentIndex === APPROVAL_FLOW.length - 1) {\n      return null\n    }\n    return APPROVAL_FLOW[currentIndex + 1]\n  }\n\n  /**\n   * 检查是否为最后一个审批节点\n   * @param {number} status 当前状态\n   * @returns {boolean}\n   */\n  static isLastApprovalNode(status) {\n    return status === APPROVAL_STATUS.GENERAL_MANAGER\n  }\n\n  /**\n   * 检查是否为最终状态（已完成或已拒绝）\n   * @param {number} status 状态\n   * @returns {boolean}\n   */\n  static isFinalStatus(status) {\n    return status === APPROVAL_STATUS.APPROVED || status === APPROVAL_STATUS.REJECTED\n  }\n\n  /**\n   * 检查是否可以进行审批操作\n   * @param {number} status 当前状态\n   * @returns {boolean}\n   */\n  static canApprove(status) {\n    return APPROVAL_FLOW.includes(status)\n  }\n\n  /**\n   * 处理审批通过\n   * @param {number} currentStatus 当前状态\n   * @returns {number} 新状态\n   */\n  static handleApprove(currentStatus) {\n    if (!this.canApprove(currentStatus)) {\n      throw new Error('当前状态不允许审批操作')\n    }\n\n    // 如果是最后一个审批节点，设置为全部同意\n    if (this.isLastApprovalNode(currentStatus)) {\n      return APPROVAL_STATUS.APPROVED\n    }\n\n    // 否则进入下一个审批节点\n    const nextStatus = this.getNextStatus(currentStatus)\n    if (nextStatus === null) {\n      throw new Error('无法获取下一个审批状态')\n    }\n\n    return nextStatus\n  }\n\n  /**\n   * 处理审批拒绝\n   * @param {number} currentStatus 当前状态\n   * @returns {number} 新状态（始终为已拒绝）\n   */\n  static handleReject(currentStatus) {\n    if (!this.canApprove(currentStatus)) {\n      throw new Error('当前状态不允许审批操作')\n    }\n    return APPROVAL_STATUS.REJECTED\n  }\n\n  /**\n   * 开始审批流程\n   * @returns {number} 第一个审批节点状态\n   */\n  static startApprovalFlow() {\n    return APPROVAL_FLOW[0]\n  }\n\n  /**\n   * 获取状态描述文本\n   * @param {number} status 状态码\n   * @returns {string} 状态描述\n   */\n  static getStatusText(status) {\n    return APPROVAL_STATUS_TEXT[status] || '未知状态'\n  }\n\n  /**\n   * 获取状态对应的标签类型（用于UI显示）\n   * @param {number} status 状态码\n   * @returns {string} 标签类型\n   */\n  static getStatusTagType(status) {\n    switch (status) {\n      case APPROVAL_STATUS.PENDING:\n        return 'info'\n      case APPROVAL_STATUS.APPROVED:\n        return 'success'\n      case APPROVAL_STATUS.REJECTED:\n        return 'danger'\n      case APPROVAL_STATUS.LEGAL_SUPERVISOR:\n      case APPROVAL_STATUS.DIRECTOR:\n      case APPROVAL_STATUS.FINANCE_SUPERVISOR:\n      case APPROVAL_STATUS.GENERAL_MANAGER:\n        return 'warning'\n      default:\n        return 'info'\n    }\n  }\n\n  /**\n   * 验证状态转换是否合法\n   * @param {number} fromStatus 原状态\n   * @param {number} toStatus 目标状态\n   * @returns {boolean} 是否合法\n   */\n  static isValidStatusTransition(fromStatus, toStatus) {\n    // 已完成或已拒绝的状态不能再转换\n    if (this.isFinalStatus(fromStatus)) {\n      return false\n    }\n\n    // 只能转换为拒绝状态或下一个审批节点\n    if (toStatus === APPROVAL_STATUS.REJECTED) {\n      return this.canApprove(fromStatus)\n    }\n\n    // 检查是否为合法的下一个状态\n    if (this.isLastApprovalNode(fromStatus)) {\n      return toStatus === APPROVAL_STATUS.APPROVED\n    }\n\n    return toStatus === this.getNextStatus(fromStatus)\n  }\n}\n\nexport default ApprovalManager\n"], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACO,IAAMA,eAAe,GAAAC,OAAA,CAAAD,eAAA,GAAG;EAC7BE,OAAO,EAAE,CAAC;EAAY;EACtBC,QAAQ,EAAE,CAAC;EAAW;EACtBC,QAAQ,EAAE,CAAC;EAAW;EACtBC,gBAAgB,EAAE,CAAC;EAAG;EACtBC,QAAQ,EAAE,CAAC;EAAW;EACtBC,kBAAkB,EAAE,CAAC;EAAE;EACvBC,eAAe,EAAE,CAAC,CAAI;AACxB,CAAC;;AAED;AACO,IAAMC,oBAAoB,GAAAR,OAAA,CAAAQ,oBAAA,OAAAC,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAC9BX,eAAe,CAACE,OAAO,EAAG,KAAK,GAC/BF,eAAe,CAACG,QAAQ,EAAG,MAAM,GACjCH,eAAe,CAACI,QAAQ,EAAG,KAAK,GAChCJ,eAAe,CAACK,gBAAgB,EAAG,QAAQ,GAC3CL,eAAe,CAACM,QAAQ,EAAG,MAAM,GACjCN,eAAe,CAACO,kBAAkB,EAAG,WAAW,GAChDP,eAAe,CAACQ,eAAe,EAAG,WAAW,CAC/C;;AAED;AACO,IAAMI,aAAa,GAAAX,OAAA,CAAAW,aAAA,GAAG,CAC3BZ,eAAe,CAACK,gBAAgB;AAAI;AACpCL,eAAe,CAACM,QAAQ;AAAY;AACpCN,eAAe,CAACO,kBAAkB;AAAE;AACpCP,eAAe,CAACQ,eAAe,CAAK;AAAA,CACrC;;AAED;AACA;AACA;AAFA,IAGaK,eAAe,GAAAZ,OAAA,CAAAY,eAAA;EAAA,SAAAA,gBAAA;IAAA,IAAAC,gBAAA,CAAAH,OAAA,QAAAE,eAAA;EAAA;EAAA,WAAAE,aAAA,CAAAJ,OAAA,EAAAE,eAAA;IAAAG,GAAA;IAAAC,KAAA;IAE1B;AACF;AACA;AACA;AACA;IACE,SAAOC,aAAaA,CAACC,aAAa,EAAE;MAClC,IAAMC,YAAY,GAAGR,aAAa,CAACS,OAAO,CAACF,aAAa,CAAC;MACzD,IAAIC,YAAY,KAAK,CAAC,CAAC,IAAIA,YAAY,KAAKR,aAAa,CAACU,MAAM,GAAG,CAAC,EAAE;QACpE,OAAO,IAAI;MACb;MACA,OAAOV,aAAa,CAACQ,YAAY,GAAG,CAAC,CAAC;IACxC;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAAJ,GAAA;IAAAC,KAAA,EAKA,SAAOM,kBAAkBA,CAACC,MAAM,EAAE;MAChC,OAAOA,MAAM,KAAKxB,eAAe,CAACQ,eAAe;IACnD;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAAQ,GAAA;IAAAC,KAAA,EAKA,SAAOQ,aAAaA,CAACD,MAAM,EAAE;MAC3B,OAAOA,MAAM,KAAKxB,eAAe,CAACG,QAAQ,IAAIqB,MAAM,KAAKxB,eAAe,CAACI,QAAQ;IACnF;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAAY,GAAA;IAAAC,KAAA,EAKA,SAAOS,UAAUA,CAACF,MAAM,EAAE;MACxB,OAAOZ,aAAa,CAACe,QAAQ,CAACH,MAAM,CAAC;IACvC;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAAR,GAAA;IAAAC,KAAA,EAKA,SAAOW,aAAaA,CAACT,aAAa,EAAE;MAClC,IAAI,CAAC,IAAI,CAACO,UAAU,CAACP,aAAa,CAAC,EAAE;QACnC,MAAM,IAAIU,KAAK,CAAC,aAAa,CAAC;MAChC;;MAEA;MACA,IAAI,IAAI,CAACN,kBAAkB,CAACJ,aAAa,CAAC,EAAE;QAC1C,OAAOnB,eAAe,CAACG,QAAQ;MACjC;;MAEA;MACA,IAAM2B,UAAU,GAAG,IAAI,CAACZ,aAAa,CAACC,aAAa,CAAC;MACpD,IAAIW,UAAU,KAAK,IAAI,EAAE;QACvB,MAAM,IAAID,KAAK,CAAC,aAAa,CAAC;MAChC;MAEA,OAAOC,UAAU;IACnB;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAAd,GAAA;IAAAC,KAAA,EAKA,SAAOc,YAAYA,CAACZ,aAAa,EAAE;MACjC,IAAI,CAAC,IAAI,CAACO,UAAU,CAACP,aAAa,CAAC,EAAE;QACnC,MAAM,IAAIU,KAAK,CAAC,aAAa,CAAC;MAChC;MACA,OAAO7B,eAAe,CAACI,QAAQ;IACjC;;IAEA;AACF;AACA;AACA;EAHE;IAAAY,GAAA;IAAAC,KAAA,EAIA,SAAOe,iBAAiBA,CAAA,EAAG;MACzB,OAAOpB,aAAa,CAAC,CAAC,CAAC;IACzB;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAAI,GAAA;IAAAC,KAAA,EAKA,SAAOgB,aAAaA,CAACT,MAAM,EAAE;MAC3B,OAAOf,oBAAoB,CAACe,MAAM,CAAC,IAAI,MAAM;IAC/C;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAAR,GAAA;IAAAC,KAAA,EAKA,SAAOiB,gBAAgBA,CAACV,MAAM,EAAE;MAC9B,QAAQA,MAAM;QACZ,KAAKxB,eAAe,CAACE,OAAO;UAC1B,OAAO,MAAM;QACf,KAAKF,eAAe,CAACG,QAAQ;UAC3B,OAAO,SAAS;QAClB,KAAKH,eAAe,CAACI,QAAQ;UAC3B,OAAO,QAAQ;QACjB,KAAKJ,eAAe,CAACK,gBAAgB;QACrC,KAAKL,eAAe,CAACM,QAAQ;QAC7B,KAAKN,eAAe,CAACO,kBAAkB;QACvC,KAAKP,eAAe,CAACQ,eAAe;UAClC,OAAO,SAAS;QAClB;UACE,OAAO,MAAM;MACjB;IACF;;IAEA;AACF;AACA;AACA;AACA;AACA;EALE;IAAAQ,GAAA;IAAAC,KAAA,EAMA,SAAOkB,uBAAuBA,CAACC,UAAU,EAAEC,QAAQ,EAAE;MACnD;MACA,IAAI,IAAI,CAACZ,aAAa,CAACW,UAAU,CAAC,EAAE;QAClC,OAAO,KAAK;MACd;;MAEA;MACA,IAAIC,QAAQ,KAAKrC,eAAe,CAACI,QAAQ,EAAE;QACzC,OAAO,IAAI,CAACsB,UAAU,CAACU,UAAU,CAAC;MACpC;;MAEA;MACA,IAAI,IAAI,CAACb,kBAAkB,CAACa,UAAU,CAAC,EAAE;QACvC,OAAOC,QAAQ,KAAKrC,eAAe,CAACG,QAAQ;MAC9C;MAEA,OAAOkC,QAAQ,KAAK,IAAI,CAACnB,aAAa,CAACkB,UAAU,CAAC;IACpD;EAAC;AAAA;AAAA,IAAAE,QAAA,GAAArC,OAAA,CAAAU,OAAA,GAGYE,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}