/**
 * 法诉费用审批流程状态管理系统
 * 
 * 审批状态定义：
 * 0: 未审批
 * 1: 全部同意（最终完成状态）
 * 2: 已拒绝（最终拒绝状态）
 * 3: 法诉主管审批（当前审批节点）
 * 4: 总监审批（当前审批节点）
 * 5: 财务主管/总监抄送（当前审批节点）
 * 6: 总经理/董事长审批(抄送)（当前审批节点）
 */

// 审批状态常量
export const LITIGATION_APPROVAL_STATUS = {
  PENDING: '0',           // 未审批
  APPROVED: '1',          // 全部同意
  REJECTED: '2',          // 已拒绝
  LEGAL_SUPERVISOR: '3',  // 法诉主管审批
  DIRECTOR: '4',          // 总监审批
  FINANCE_SUPERVISOR: '5', // 财务主管/总监抄送
  GENERAL_MANAGER: '6'    // 总经理/董事长审批
}

// 审批状态描述
export const LITIGATION_APPROVAL_STATUS_TEXT = {
  [LITIGATION_APPROVAL_STATUS.PENDING]: '未审批',
  [LITIGATION_APPROVAL_STATUS.APPROVED]: '全部同意',
  [LITIGATION_APPROVAL_STATUS.REJECTED]: '已拒绝',
  [LITIGATION_APPROVAL_STATUS.LEGAL_SUPERVISOR]: '法诉主管审批',
  [LITIGATION_APPROVAL_STATUS.DIRECTOR]: '总监审批',
  [LITIGATION_APPROVAL_STATUS.FINANCE_SUPERVISOR]: '财务主管/总监抄送',
  [LITIGATION_APPROVAL_STATUS.GENERAL_MANAGER]: '总经理/董事长审批'
}

// 审批流程顺序
export const LITIGATION_APPROVAL_FLOW = [
  LITIGATION_APPROVAL_STATUS.LEGAL_SUPERVISOR,   // 法诉主管审批
  LITIGATION_APPROVAL_STATUS.DIRECTOR,           // 总监审批
  LITIGATION_APPROVAL_STATUS.FINANCE_SUPERVISOR, // 财务主管/总监抄送
  LITIGATION_APPROVAL_STATUS.GENERAL_MANAGER     // 总经理/董事长审批
]

/**
 * 法诉费用审批流程管理类
 */
export class LitigationApprovalManager {
  
  /**
   * 获取下一个审批状态
   * @param {string} currentStatus 当前状态
   * @returns {string|null} 下一个状态，如果没有下一个状态返回null
   */
  static getNextStatus(currentStatus) {
    const currentIndex = LITIGATION_APPROVAL_FLOW.indexOf(currentStatus)
    if (currentIndex === -1 || currentIndex === LITIGATION_APPROVAL_FLOW.length - 1) {
      return null
    }
    return LITIGATION_APPROVAL_FLOW[currentIndex + 1]
  }

  /**
   * 检查是否为最后一个审批节点
   * @param {string} status 当前状态
   * @returns {boolean}
   */
  static isLastApprovalNode(status) {
    return status === LITIGATION_APPROVAL_STATUS.GENERAL_MANAGER
  }

  /**
   * 检查是否为最终状态（已完成或已拒绝）
   * @param {string} status 状态
   * @returns {boolean}
   */
  static isFinalStatus(status) {
    return status === LITIGATION_APPROVAL_STATUS.APPROVED || status === LITIGATION_APPROVAL_STATUS.REJECTED
  }

  /**
   * 检查是否可以进行审批操作
   * @param {string} status 当前状态
   * @returns {boolean}
   */
  static canApprove(status) {
    return LITIGATION_APPROVAL_FLOW.includes(status)
  }

  /**
   * 处理审批通过
   * @param {string} currentStatus 当前状态
   * @returns {string} 新状态
   */
  static handleApprove(currentStatus) {
    if (!this.canApprove(currentStatus)) {
      throw new Error('当前状态不允许审批操作')
    }

    // 如果是最后一个审批节点，设置为全部同意
    if (this.isLastApprovalNode(currentStatus)) {
      return LITIGATION_APPROVAL_STATUS.APPROVED
    }

    // 否则进入下一个审批节点
    const nextStatus = this.getNextStatus(currentStatus)
    if (nextStatus === null) {
      throw new Error('无法获取下一个审批状态')
    }

    return nextStatus
  }

  /**
   * 处理审批拒绝
   * @param {string} currentStatus 当前状态
   * @returns {string} 新状态（始终为已拒绝）
   */
  static handleReject(currentStatus) {
    if (!this.canApprove(currentStatus)) {
      throw new Error('当前状态不允许审批操作')
    }
    return LITIGATION_APPROVAL_STATUS.REJECTED
  }

  /**
   * 开始审批流程
   * @returns {string} 第一个审批节点状态
   */
  static startApprovalFlow() {
    return LITIGATION_APPROVAL_FLOW[0]
  }

  /**
   * 获取状态描述文本
   * @param {string} status 状态码
   * @returns {string} 状态描述
   */
  static getStatusText(status) {
    return LITIGATION_APPROVAL_STATUS_TEXT[status] || '未知状态'
  }

  /**
   * 获取状态对应的标签类型（用于UI显示）
   * @param {string} status 状态码
   * @returns {string} 标签类型
   */
  static getStatusTagType(status) {
    switch (status) {
      case LITIGATION_APPROVAL_STATUS.PENDING:
        return 'info'
      case LITIGATION_APPROVAL_STATUS.APPROVED:
        return 'success'
      case LITIGATION_APPROVAL_STATUS.REJECTED:
        return 'danger'
      case LITIGATION_APPROVAL_STATUS.LEGAL_SUPERVISOR:
      case LITIGATION_APPROVAL_STATUS.DIRECTOR:
      case LITIGATION_APPROVAL_STATUS.FINANCE_SUPERVISOR:
      case LITIGATION_APPROVAL_STATUS.GENERAL_MANAGER:
        return 'warning'
      default:
        return 'info'
    }
  }

  /**
   * 验证状态转换是否合法
   * @param {string} fromStatus 原状态
   * @param {string} toStatus 目标状态
   * @returns {boolean} 是否合法
   */
  static isValidStatusTransition(fromStatus, toStatus) {
    // 已完成或已拒绝的状态不能再转换
    if (this.isFinalStatus(fromStatus)) {
      return false
    }

    // 只能转换为拒绝状态或下一个审批节点
    if (toStatus === LITIGATION_APPROVAL_STATUS.REJECTED) {
      return this.canApprove(fromStatus)
    }

    // 检查是否为合法的下一个状态
    if (this.isLastApprovalNode(fromStatus)) {
      return toStatus === LITIGATION_APPROVAL_STATUS.APPROVED
    }

    return toStatus === this.getNextStatus(fromStatus)
  }
}

export default LitigationApprovalManager
