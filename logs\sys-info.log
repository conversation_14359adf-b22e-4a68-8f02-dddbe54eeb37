08:43:03.760 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 19508 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
08:43:03.763 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
08:43:03.768 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
08:43:06.310 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
08:43:06.312 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
08:43:06.313 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
08:43:06.399 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
08:43:08.207 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
08:43:08.928 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
08:43:11.892 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
08:43:11.900 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
08:43:11.900 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
08:43:11.901 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
08:43:11.902 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

08:43:11.902 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
08:43:11.902 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
08:43:11.902 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7d84f98
08:43:11.996 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
08:43:11.996 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
08:43:11.999 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
08:43:12.858 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
08:43:13.210 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
08:43:13.222 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.808 seconds (JVM running for 10.625)
08:43:13.224 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
08:43:13.225 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
08:43:13.225 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
08:43:13.225 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
08:43:13.228 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
08:43:13.229 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
08:43:13.229 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
08:43:13.229 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
08:43:13.229 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
08:43:13.229 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
08:45:48.080 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
08:45:48.763 [http-nio-8081-exec-1] INFO  c.r.w.c.w.WxLoginController - [wxLogin,79] - 获取到用户openid: o07uy7WkY8Q5Oxv1pjVPPWIOfAS8
08:45:48.798 [http-nio-8081-exec-1] INFO  c.r.w.c.w.WxLoginController - [wxLogin,89] - 成功解密手机号: ***********
08:45:48.846 [http-nio-8081-exec-1] INFO  c.r.w.c.w.WxLoginController - [wxLogin,158] - 手机号 *********** 未找到对应用户
08:48:16.546 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
10:39:34.722 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 15900 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
10:39:34.726 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
10:39:34.729 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:39:37.263 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
10:39:37.267 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:39:37.267 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
10:39:37.352 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:39:39.188 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
10:39:39.915 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
10:39:42.902 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:39:42.912 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:39:42.912 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:39:42.913 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:39:42.914 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:39:42.914 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:39:42.914 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:39:42.914 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@61e87312
10:39:43.015 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
10:39:43.016 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
10:39:43.019 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
10:39:43.901 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
10:39:44.216 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:39:44.225 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.861 seconds (JVM running for 10.571)
10:39:44.227 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
10:39:44.228 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
10:39:44.228 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
10:39:44.228 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
10:39:44.231 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
10:39:44.232 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
10:39:44.232 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
10:39:44.232 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
10:39:44.232 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
10:39:44.233 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
11:00:15.155 [http-nio-8081-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:00:19.008 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
11:02:39.862 [schedule-pool-2] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Logout][退出成功]
11:02:52.104 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[chudanyuan][Success][登录成功]
11:03:20.192 [schedule-pool-3] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[chudanyuan][Logout][退出成功]
11:03:22.991 [schedule-pool-2] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Error][验证码错误]
11:03:28.077 [schedule-pool-4] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
11:07:02.040 [http-nio-8081-exec-34] INFO  c.r.w.c.w.WxLoginController - [wxLogin,79] - 获取到用户openid: o07uy7WkY8Q5Oxv1pjVPPWIOfAS8
11:07:02.040 [http-nio-8081-exec-34] INFO  c.r.w.c.w.WxLoginController - [wxLogin,167] - 用户未授权手机号，openid: o07uy7WkY8Q5Oxv1pjVPPWIOfAS8
11:09:01.882 [http-nio-8081-exec-16] INFO  c.r.w.c.w.WxLoginController - [wxLogin,79] - 获取到用户openid: o07uy7WkY8Q5Oxv1pjVPPWIOfAS8
11:09:01.886 [http-nio-8081-exec-16] INFO  c.r.w.c.w.WxLoginController - [wxLogin,89] - 成功解密手机号: ***********
11:09:01.905 [http-nio-8081-exec-16] INFO  c.r.w.c.w.WxLoginController - [wxLogin,158] - 手机号 *********** 未找到对应用户
11:11:09.207 [http-nio-8081-exec-40] INFO  c.r.w.c.w.WxLoginController - [wxLogin,79] - 获取到用户openid: o07uy7WkY8Q5Oxv1pjVPPWIOfAS8
11:11:09.208 [http-nio-8081-exec-40] INFO  c.r.w.c.w.WxLoginController - [wxLogin,89] - 成功解密手机号: ***********
11:11:09.282 [http-nio-8081-exec-40] INFO  c.r.w.c.w.WxLoginController - [wxLogin,131] - 用户 chudanyuan 微信登录成功，角色: 出单员工, token长度: 210
11:14:22.706 [schedule-pool-1] INFO  sys-user - [run,55] - [************]内网IP[yidianadmin][Success][登录成功]
11:14:31.816 [http-nio-8081-exec-54] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:LoanReminder(id=5, loanId=6, userId=null, identity=电催员工, customerName=陈XX, customerMobile=null, carStatus=8, repaymentStatus=2, examineStatus=0, examineReason=null, bMoney=111.00, dMoney=222.00, bRepaymentImg=https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/e719f5cc-2491-4b9a-93ef-130ada39c854.jpg, dRepaymentImg=https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/218c5385-c24a-4618-8604-6340db686af2.jpg,https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/********-e063-42ec-9337-7961ddb7af07.jpg, bAccount=345345, dAccount=345345, urgeStatus=3, urgeDescribe=123123, urgeMoney=null, appointedTime=Wed Jun 25 00:00:00 CST 2025, trackingTime=null, oMoney=null, pMoney=null, pRepaymentImg=null, pAccount=null, cMoney=null, oRepaymentImg=null, cRepaymentImg=null, oAccount=null, cAccount=null, fundsRepayment=null, fundsAmount=null, fundsAccountType=null, fundsImage=null, litigationId=null, status=null, borrower=彭XX, issuingChannel=衢州华沂分公司, lendingBank=皖新租赁, alLendingBank=皖新租赁, alBMoney=null, alDMoney=null, bOverdueAmount=null, dOverdueAmount=null, customerId=PC0000000006, applyId=***************************, certId=3621X51XX809X4X71X, plateNo=浙JXXJX1, salesman=system, jgName=衢州华沂分公司, followUp=null, bankyqMoney=762.57, dkyqMoney=123.00, productName=测试产品, nextInstalmentAmt=1969.06, bNowMoney=762.57, dNowMoney=820.76, bRepaymentAmounts=0.00, dRepaymentAmounts=1.00, bReturnTime=1970-01-01, dReturnTime=1970-01-01, startTime=null, endTime=null, createBy=admin, createTime=Wed Jun 25 13:29:07 CST 2025, updateBy=, updateTime=Tue Jul 15 20:10:29 CST 2025)
11:14:31.840 [http-nio-8081-exec-54] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
11:14:31.859 [http-nio-8081-exec-54] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
11:14:31.878 [http-nio-8081-exec-54] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
11:14:31.897 [http-nio-8081-exec-54] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:LoanReminder(id=7, loanId=2, userId=null, identity=电催员工, customerName=袁XX, customerMobile=null, carStatus=2, repaymentStatus=null, examineStatus=1, examineReason=null, bMoney=null, dMoney=null, bRepaymentImg=null, dRepaymentImg=null, bAccount=null, dAccount=null, urgeStatus=2, urgeDescribe=null, urgeMoney=null, appointedTime=null, trackingTime=null, oMoney=null, pMoney=null, pRepaymentImg=null, pAccount=null, cMoney=null, oRepaymentImg=null, cRepaymentImg=null, oAccount=null, cAccount=null, fundsRepayment=null, fundsAmount=null, fundsAccountType=null, fundsImage=null, litigationId=null, status=null, borrower=袁XX, issuingChannel=衢州华沂分公司, lendingBank=浙商银行, alLendingBank=浙商银行, alBMoney=null, alDMoney=null, bOverdueAmount=null, dOverdueAmount=null, customerId=PC0000000002, applyId=***************************, certId=3X01241X96041X03X5, plateNo=浙JXXJX2, salesman=system, jgName=衢州华沂分公司, followUp=null, bankyqMoney=3386.57, dkyqMoney=123.00, productName=测试产品, nextInstalmentAmt=1969.06, bNowMoney=1685.70, dNowMoney=820.76, bRepaymentAmounts=0.00, dRepaymentAmounts=123.00, bReturnTime=1970-01-01, dReturnTime=1970-01-01, startTime=null, endTime=null, createBy=admin, createTime=Wed Jul 09 20:34:57 CST 2025, updateBy=yidianadmin, updateTime=Tue Jul 15 21:38:28 CST 2025)
11:14:31.918 [http-nio-8081-exec-54] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
12:49:07.816 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 25504 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
12:49:07.820 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
12:49:07.821 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:49:14.756 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
12:49:14.759 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
12:49:14.759 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
12:49:14.861 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
12:49:16.761 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
12:49:17.485 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
12:49:21.227 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
12:49:21.236 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
12:49:21.236 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
12:49:21.237 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
12:49:21.238 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

12:49:21.238 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
12:49:21.238 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
12:49:21.238 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3a395f8f
12:49:21.336 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
12:49:21.337 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
12:49:21.340 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
12:49:22.271 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
12:49:22.642 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
12:49:22.652 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 15.251 seconds (JVM running for 21.935)
12:49:22.655 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
12:49:22.656 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
12:49:22.656 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
12:49:22.656 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
12:49:22.659 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
12:49:22.659 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
12:49:22.659 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
12:49:22.660 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
12:49:22.660 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
12:49:22.660 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
12:49:39.663 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:49:43.803 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Error][验证码错误]
12:50:42.645 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
12:54:06.630 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 19996 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
12:54:06.633 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:54:06.633 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
12:54:09.158 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
12:54:09.161 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
12:54:09.162 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
12:54:09.252 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
12:54:11.037 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
12:54:11.744 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
12:54:14.767 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
12:54:14.776 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
12:54:14.776 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
12:54:14.777 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
12:54:14.778 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

12:54:14.778 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
12:54:14.779 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
12:54:14.779 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4aa9dbbf
12:54:14.876 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
12:54:14.876 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
12:54:14.879 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
12:54:15.684 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
12:54:16.041 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
12:54:16.051 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.761 seconds (JVM running for 10.229)
12:54:16.054 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
12:54:16.054 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
12:54:16.054 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
12:54:16.055 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
12:54:16.056 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
12:54:16.056 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
12:54:16.056 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
12:54:16.056 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
12:54:16.056 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
12:54:16.056 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
12:54:21.682 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:01:03.486 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 22424 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
13:01:03.491 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
13:01:03.493 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:01:05.972 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
13:01:05.975 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
13:01:05.976 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
13:01:06.063 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
13:01:07.843 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
13:01:08.539 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
13:01:11.563 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
13:01:11.573 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
13:01:11.574 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
13:01:11.574 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
13:01:11.575 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

13:01:11.575 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
13:01:11.575 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
13:01:11.575 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2f908183
13:01:11.664 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
13:01:11.665 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
13:01:11.668 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
13:01:12.547 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
13:01:12.915 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
13:01:12.924 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.774 seconds (JVM running for 10.268)
13:01:12.925 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
13:01:12.926 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
13:01:12.926 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
13:01:12.926 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
13:01:12.927 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
13:01:12.927 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
13:01:12.927 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
13:01:12.927 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
13:01:12.927 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
13:01:12.927 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
13:01:15.936 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:01:48.346 [http-nio-8081-exec-6] INFO  c.r.l.c.LitigationCostApprovalController - [singleApproveNew,439] - === 单个审批方法开始 ===
13:01:48.351 [http-nio-8081-exec-6] INFO  c.r.l.c.LitigationCostApprovalController - [singleApproveNew,440] - 接收到的参数: {id=12, action=approve, rejectReason=}
13:01:48.351 [http-nio-8081-exec-6] INFO  c.r.l.c.LitigationCostApprovalController - [singleApproveNew,443] - 提取的ID对象: 12, 类型: Integer
13:01:48.351 [http-nio-8081-exec-6] INFO  c.r.l.c.LitigationCostApprovalController - [singleApproveNew,453] - 转换后的ID: 12
13:01:48.351 [http-nio-8081-exec-6] INFO  c.r.l.c.LitigationCostApprovalController - [singleApproveNew,461] - 操作类型: approve, 拒绝原因: 
13:01:48.378 [http-nio-8081-exec-6] INFO  c.r.l.c.LitigationCostApprovalController - [singleApproveNew,476] - 开始查询审批记录，ID: 12
13:01:48.378 [http-nio-8081-exec-6] INFO  c.r.l.s.i.LitigationCostApprovalServiceImpl - [selectLitigationCostApprovalById,49] - === Service层查询开始 ===
13:01:48.378 [http-nio-8081-exec-6] INFO  c.r.l.s.i.LitigationCostApprovalServiceImpl - [selectLitigationCostApprovalById,50] - 查询参数ID: 12
13:01:48.410 [http-nio-8081-exec-6] INFO  c.r.l.s.i.LitigationCostApprovalServiceImpl - [selectLitigationCostApprovalById,54] - Mapper查询结果: 未找到记录
13:01:48.410 [http-nio-8081-exec-6] INFO  c.r.l.c.LitigationCostApprovalController - [singleApproveNew,478] - 查询结果: 未找到记录
13:04:26.694 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 25808 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
13:04:26.697 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:04:26.698 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
13:04:29.399 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
13:04:29.402 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
13:04:29.402 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
13:04:29.489 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
13:04:31.331 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
13:04:32.060 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
13:04:35.229 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
13:04:35.241 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
13:04:35.242 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
13:04:35.243 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
13:04:35.244 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

13:04:35.244 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
13:04:35.244 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
13:04:35.245 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3e388bf7
13:04:35.345 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
13:04:35.346 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
13:04:35.349 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
13:04:36.301 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
13:04:36.660 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
13:04:36.669 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 10.318 seconds (JVM running for 10.787)
13:04:36.671 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
13:04:36.671 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
13:04:36.671 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
13:04:36.671 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
13:04:36.672 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
13:04:36.672 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
13:04:36.672 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
13:04:36.673 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
13:04:36.673 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
13:04:36.673 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
13:04:40.719 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:04:40.966 [http-nio-8081-exec-1] INFO  c.r.l.c.LitigationCostApprovalController - [singleApproveNew,439] - === 单个审批方法开始 ===
13:04:40.967 [http-nio-8081-exec-1] INFO  c.r.l.c.LitigationCostApprovalController - [singleApproveNew,440] - 接收到的参数: {id=12, action=approve, rejectReason=}
13:04:40.967 [http-nio-8081-exec-1] INFO  c.r.l.c.LitigationCostApprovalController - [singleApproveNew,443] - 提取的ID对象: 12, 类型: Integer
13:04:40.967 [http-nio-8081-exec-1] INFO  c.r.l.c.LitigationCostApprovalController - [singleApproveNew,453] - 转换后的ID: 12
13:04:40.967 [http-nio-8081-exec-1] INFO  c.r.l.c.LitigationCostApprovalController - [singleApproveNew,461] - 操作类型: approve, 拒绝原因: 
13:04:41.001 [http-nio-8081-exec-1] INFO  c.r.l.c.LitigationCostApprovalController - [singleApproveNew,476] - 开始查询审批记录，ID: 12
13:04:41.001 [http-nio-8081-exec-1] INFO  c.r.l.s.i.LitigationCostApprovalServiceImpl - [selectLitigationCostApprovalById,49] - === Service层查询开始 ===
13:04:41.001 [http-nio-8081-exec-1] INFO  c.r.l.s.i.LitigationCostApprovalServiceImpl - [selectLitigationCostApprovalById,50] - 查询参数ID: 12
13:04:41.025 [http-nio-8081-exec-1] INFO  c.r.l.s.i.LitigationCostApprovalServiceImpl - [selectLitigationCostApprovalById,54] - Mapper查询结果: 找到记录
13:04:41.026 [http-nio-8081-exec-1] INFO  c.r.l.s.i.LitigationCostApprovalServiceImpl - [selectLitigationCostApprovalById,56] - Service层查询成功 - 记录ID: 12, 状态: 0, 案件ID: 14
13:04:41.026 [http-nio-8081-exec-1] INFO  c.r.l.c.LitigationCostApprovalController - [singleApproveNew,478] - 查询结果: 找到记录
13:04:41.026 [http-nio-8081-exec-1] INFO  c.r.l.c.LitigationCostApprovalController - [singleApproveNew,480] - 审批记录详情 - ID: 12, 状态: 0, 案件ID: 14
13:08:27.389 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 26356 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
13:08:27.393 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
13:08:27.394 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:08:29.890 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
13:08:29.892 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
13:08:29.892 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
13:08:29.969 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
13:08:31.853 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
13:08:32.612 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
13:08:35.636 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
13:08:35.644 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
13:08:35.645 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
13:08:35.646 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
13:08:35.646 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

13:08:35.646 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
13:08:35.647 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
13:08:35.647 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4fa01f38
13:08:35.731 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
13:08:35.732 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
13:08:35.734 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
13:08:36.594 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
13:08:36.975 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
13:08:36.985 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.962 seconds (JVM running for 10.432)
13:08:36.988 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
13:08:36.988 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
13:08:36.989 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
13:08:36.990 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
13:08:36.991 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
13:08:36.991 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
13:08:36.991 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
13:08:36.991 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
13:08:36.991 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
13:08:36.991 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
13:08:41.137 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:09:08.088 [http-nio-8081-exec-7] INFO  c.r.l.c.LitigationCostApprovalController - [singleApproveNew,439] - === 单个审批方法开始 ===
13:09:08.088 [http-nio-8081-exec-7] INFO  c.r.l.c.LitigationCostApprovalController - [singleApproveNew,440] - 接收到的参数: {id=8, action=approve, rejectReason=}
13:09:08.094 [http-nio-8081-exec-7] INFO  c.r.l.c.LitigationCostApprovalController - [singleApproveNew,443] - 提取的ID对象: 8, 类型: Integer
13:09:08.094 [http-nio-8081-exec-7] INFO  c.r.l.c.LitigationCostApprovalController - [singleApproveNew,453] - 转换后的ID: 8
13:09:08.094 [http-nio-8081-exec-7] INFO  c.r.l.c.LitigationCostApprovalController - [singleApproveNew,461] - 操作类型: approve, 拒绝原因: 
13:09:08.114 [http-nio-8081-exec-7] INFO  c.r.l.c.LitigationCostApprovalController - [singleApproveNew,476] - 开始查询审批记录，ID: 8
13:09:08.114 [http-nio-8081-exec-7] INFO  c.r.l.s.i.LitigationCostApprovalServiceImpl - [selectLitigationCostApprovalById,49] - === Service层查询开始 ===
13:09:08.114 [http-nio-8081-exec-7] INFO  c.r.l.s.i.LitigationCostApprovalServiceImpl - [selectLitigationCostApprovalById,50] - 查询参数ID: 8
13:09:08.134 [http-nio-8081-exec-7] INFO  c.r.l.s.i.LitigationCostApprovalServiceImpl - [selectLitigationCostApprovalById,54] - Mapper查询结果: 找到记录
13:09:08.134 [http-nio-8081-exec-7] INFO  c.r.l.s.i.LitigationCostApprovalServiceImpl - [selectLitigationCostApprovalById,56] - Service层查询成功 - 记录ID: 8, 状态: 0, 案件ID: 16
13:09:08.134 [http-nio-8081-exec-7] INFO  c.r.l.c.LitigationCostApprovalController - [singleApproveNew,478] - 查询结果: 找到记录
13:09:08.134 [http-nio-8081-exec-7] INFO  c.r.l.c.LitigationCostApprovalController - [singleApproveNew,480] - 审批记录详情 - ID: 8, 状态: 0, 案件ID: 16
13:14:44.012 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:14:44.010 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 24132 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
13:14:44.013 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
13:14:46.495 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
13:14:46.499 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
13:14:46.499 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
13:14:46.592 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
13:14:48.430 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
13:14:49.175 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
13:14:52.222 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
13:14:52.233 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
13:14:52.233 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
13:14:52.235 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
13:14:52.235 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

13:14:52.236 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
13:14:52.237 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
13:14:52.237 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6058d5d
13:14:52.344 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
13:14:52.344 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
13:14:52.347 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
13:14:53.237 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
13:14:53.575 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
13:14:53.585 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.928 seconds (JVM running for 10.405)
13:14:53.587 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
13:14:53.587 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
13:14:53.587 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
13:14:53.587 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
13:14:53.588 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
13:14:53.589 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
13:14:53.589 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
13:14:53.590 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
13:14:53.590 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
13:14:53.590 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
13:14:55.725 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:16:47.275 [schedule-pool-2] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Logout][退出成功]
13:17:02.793 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[fasuzhuguan][Success][登录成功]
13:17:11.307 [schedule-pool-3] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[fasuzhuguan][Logout][退出成功]
13:17:15.505 [schedule-pool-2] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
13:17:51.369 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Logout][退出成功]
13:18:03.216 [http-nio-8081-exec-43] INFO  c.r.f.w.s.UserDetailsServiceImpl - [loadUserByUsername,43] - 登录用户：fashuzhuguan 不存在.
13:18:03.243 [schedule-pool-5] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[fashuzhuguan][Error][用户不存在/密码错误]
13:18:10.947 [schedule-pool-3] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[fasuzhuguan][Success][登录成功]
13:18:22.947 [schedule-pool-6] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[fasuzhuguan][Logout][退出成功]
13:18:26.446 [schedule-pool-2] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Error][验证码错误]
13:18:29.821 [schedule-pool-7] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
13:20:50.216 [schedule-pool-4] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Logout][退出成功]
13:20:59.474 [schedule-pool-8] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
13:21:14.389 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Logout][退出成功]
13:21:27.970 [schedule-pool-9] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[fasuzhuguan][Success][登录成功]
13:21:50.830 [schedule-pool-5] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[fasuzhuguan][Logout][退出成功]
13:21:53.666 [schedule-pool-10] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
13:22:19.146 [schedule-pool-3] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Logout][退出成功]
13:22:33.454 [schedule-pool-11] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[fasuzhuguan][Error][验证码错误]
13:22:36.840 [schedule-pool-6] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[fasuzhuguan][Success][登录成功]
13:23:31.535 [schedule-pool-12] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[fasuzhuguan][Logout][退出成功]
13:23:34.571 [schedule-pool-2] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
13:25:21.452 [schedule-pool-14] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Logout][退出成功]
13:25:24.868 [schedule-pool-4] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
13:25:28.921 [schedule-pool-15] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Logout][退出成功]
13:25:41.821 [schedule-pool-8] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[fasuzhuguan][Success][登录成功]
13:27:32.458 [schedule-pool-16] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[fasuzhuguan][Logout][退出成功]
13:27:35.574 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
13:29:28.474 [schedule-pool-9] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Logout][退出成功]
13:29:31.557 [schedule-pool-18] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
13:29:35.606 [schedule-pool-5] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Logout][退出成功]
13:29:49.011 [schedule-pool-19] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[fasuzhuguan][Success][登录成功]
13:30:26.929 [schedule-pool-10] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[fasuzhuguan][Logout][退出成功]
13:30:29.340 [schedule-pool-20] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Error][验证码错误]
13:30:32.658 [schedule-pool-3] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Error][验证码错误]
13:30:36.271 [schedule-pool-21] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
13:38:38.781 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 10916 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
13:38:38.785 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
13:38:38.788 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:38:41.349 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
13:38:41.351 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
13:38:41.351 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
13:38:41.441 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
13:38:43.282 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
13:38:43.970 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
13:38:46.969 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
13:38:46.978 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
13:38:46.978 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
13:38:46.982 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
13:38:46.984 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

13:38:46.984 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
13:38:46.984 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
13:38:46.984 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2b9ab438
13:38:47.074 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
13:38:47.074 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
13:38:47.077 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
13:38:47.951 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
13:38:48.290 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
13:38:48.300 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.889 seconds (JVM running for 10.388)
13:38:48.302 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
13:38:48.302 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
13:38:48.302 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
13:38:48.302 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
13:38:48.303 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
13:38:48.304 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
13:38:48.304 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
13:38:48.304 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
13:38:48.304 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
13:38:48.304 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
13:38:53.391 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:38:53.608 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Logout][退出成功]
13:38:59.885 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
13:39:05.057 [schedule-pool-2] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Logout][退出成功]
13:39:20.356 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[fasuzhuguan][Success][登录成功]
13:40:31.853 [schedule-pool-3] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[fasuzhuguan][Logout][退出成功]
13:40:35.334 [schedule-pool-2] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Error][验证码错误]
13:40:40.171 [schedule-pool-4] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
13:46:11.694 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 21840 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
13:46:11.697 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:46:11.698 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
13:46:14.438 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
13:46:14.441 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
13:46:14.441 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
13:46:14.540 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
13:46:16.374 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
13:46:17.100 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
13:46:20.195 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
13:46:20.205 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
13:46:20.205 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
13:46:20.206 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
13:46:20.207 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

13:46:20.207 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
13:46:20.207 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
13:46:20.207 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@755abfce
13:46:20.294 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
13:46:20.295 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
13:46:20.298 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
13:46:21.174 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
13:46:21.523 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
13:46:21.533 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 10.223 seconds (JVM running for 10.761)
13:46:21.535 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
13:46:21.536 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
13:46:21.536 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
13:46:21.536 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
13:46:21.538 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
13:46:21.538 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
13:46:21.538 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
13:46:21.539 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
13:46:21.539 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
13:46:21.539 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
13:46:23.856 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:50:01.133 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 18320 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
13:50:01.137 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
13:50:01.137 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:50:03.705 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
13:50:03.707 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
13:50:03.708 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
13:50:03.798 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
13:50:05.587 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
13:50:06.284 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
13:50:09.370 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
13:50:09.380 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
13:50:09.380 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
13:50:09.382 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
13:50:09.383 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

13:50:09.383 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
13:50:09.383 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
13:50:09.383 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@faaa5d
13:50:09.478 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
13:50:09.478 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
13:50:09.481 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
13:50:10.391 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
13:50:10.751 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
13:50:10.762 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.986 seconds (JVM running for 10.52)
13:50:10.764 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
13:50:10.764 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
13:50:10.764 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
13:50:10.764 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
13:50:10.765 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
13:50:10.765 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
13:50:10.766 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
13:50:10.766 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
13:50:10.766 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
13:50:10.766 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
13:50:27.429 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:58:52.690 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 19620 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
13:58:52.694 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
13:58:52.696 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:58:55.230 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
13:58:55.234 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
13:58:55.235 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
13:58:55.322 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
13:58:57.078 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
13:58:57.774 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
13:59:01.004 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
13:59:01.012 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
13:59:01.013 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
13:59:01.014 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
13:59:01.015 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

13:59:01.015 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
13:59:01.016 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
13:59:01.016 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@12f26cd8
13:59:01.109 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
13:59:01.109 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
13:59:01.113 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
13:59:01.959 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
13:59:02.298 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
13:59:02.308 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.96 seconds (JVM running for 10.449)
13:59:02.310 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
13:59:02.311 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
13:59:02.312 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
13:59:02.312 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
13:59:02.313 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
13:59:02.313 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
13:59:02.313 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
13:59:02.313 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
13:59:02.313 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
13:59:02.313 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
13:59:05.903 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:01:06.255 [schedule-pool-3] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Logout][退出成功]
14:01:24.128 [schedule-pool-2] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[fasuzhuguan][Success][登录成功]
14:01:53.410 [schedule-pool-4] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[fasuzhuguan][Logout][退出成功]
14:01:56.037 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
14:03:13.933 [schedule-pool-3] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Logout][退出成功]
14:03:16.186 [schedule-pool-6] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
14:03:21.950 [schedule-pool-2] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Logout][退出成功]
14:03:32.333 [schedule-pool-7] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[fasuzhuguan][Success][登录成功]
14:19:52.849 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 22660 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
14:19:52.853 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
14:19:52.853 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:19:55.515 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
14:19:55.524 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
14:19:55.524 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
14:19:55.617 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
14:19:57.429 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
14:19:58.158 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
14:20:01.159 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:20:01.169 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:20:01.170 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:20:01.171 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:20:01.171 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:20:01.172 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:20:01.172 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:20:01.172 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@faaa5d
14:20:01.260 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
14:20:01.260 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
14:20:01.263 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
14:20:02.112 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
14:20:02.448 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:20:02.462 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.945 seconds (JVM running for 10.405)
14:20:02.464 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
14:20:02.464 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
14:20:02.464 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
14:20:02.466 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
14:20:02.466 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
14:20:02.466 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
14:20:02.466 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
14:20:02.467 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
14:20:02.467 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
14:20:02.467 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
14:20:33.036 [http-nio-8081-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:33:20.138 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 27276 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
14:33:20.140 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:33:20.141 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
14:33:22.711 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
14:33:22.713 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
14:33:22.713 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
14:33:22.798 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
14:33:24.833 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
14:33:25.661 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
14:33:28.610 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:33:28.622 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:33:28.622 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:33:28.623 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:33:28.623 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:33:28.624 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:33:28.624 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:33:28.624 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@494506d4
14:33:28.726 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
14:33:28.727 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
14:33:28.730 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
14:33:29.610 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
14:33:29.925 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:33:29.934 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 10.138 seconds (JVM running for 10.621)
14:33:29.937 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
14:33:29.938 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
14:33:29.939 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
14:33:29.939 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
14:33:29.941 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
14:33:29.941 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
14:33:29.941 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
14:33:29.941 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
14:33:29.941 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
14:33:29.942 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
14:34:14.560 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:37:00.992 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 25676 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
14:37:00.995 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:37:00.995 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
14:37:03.604 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
14:37:03.607 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
14:37:03.607 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
14:37:03.692 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
14:37:06.442 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
14:37:07.696 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
14:37:10.766 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:37:10.775 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:37:10.775 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:37:10.776 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:37:10.777 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:37:10.777 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:37:10.777 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:37:10.777 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@faaa5d
14:37:10.884 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
14:37:10.884 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
14:37:10.887 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
14:37:11.722 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
14:37:12.063 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:37:12.076 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 11.459 seconds (JVM running for 12.012)
14:37:12.079 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
14:37:12.080 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
14:37:12.080 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
14:37:12.080 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
14:37:12.082 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
14:37:12.082 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
14:37:12.083 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
14:37:12.086 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
14:37:12.086 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
14:37:12.086 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
14:37:15.587 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:46:53.219 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 23340 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
14:46:53.222 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
14:46:53.225 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:46:55.751 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
14:46:55.754 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
14:46:55.754 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
14:46:55.847 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
14:46:57.622 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
14:46:58.308 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
14:47:01.450 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:47:01.458 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:47:01.458 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:47:01.459 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:47:01.460 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:47:01.460 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:47:01.460 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:47:01.460 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2d649cdb
14:47:01.553 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
14:47:01.553 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
14:47:01.556 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
14:47:02.430 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
14:47:02.796 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:47:02.807 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.938 seconds (JVM running for 10.414)
14:47:02.812 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
14:47:02.812 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
14:47:02.813 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
14:47:02.813 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
14:47:02.815 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
14:47:02.815 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
14:47:02.815 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
14:47:02.815 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
14:47:02.816 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
14:47:02.816 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
14:47:09.081 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:51:29.659 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 27172 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
14:51:29.661 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:51:29.663 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
14:51:32.489 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
14:51:32.492 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
14:51:32.492 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
14:51:32.580 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
14:51:34.350 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
14:51:35.049 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
14:51:38.037 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:51:38.047 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:51:38.047 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:51:38.049 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:51:38.051 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:51:38.052 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:51:38.053 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:51:38.053 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6b18adc4
14:51:38.139 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
14:51:38.140 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
14:51:38.143 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
14:51:39.019 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
14:51:39.372 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:51:39.382 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 10.109 seconds (JVM running for 10.684)
14:51:39.384 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
14:51:39.384 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
14:51:39.384 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
14:51:39.384 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
14:51:39.385 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
14:51:39.385 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
14:51:39.385 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
14:51:39.386 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
14:51:39.386 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
14:51:39.386 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
14:53:05.823 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:00:52.368 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 27616 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
15:00:52.372 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
15:00:52.375 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:00:54.833 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
15:00:54.836 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
15:00:54.836 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
15:00:54.931 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
15:00:56.632 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
15:00:57.353 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
15:01:00.332 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:01:00.343 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:01:00.344 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:01:00.345 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
15:01:00.345 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

15:01:00.346 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
15:01:00.346 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:01:00.346 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@755abfce
15:01:00.433 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
15:01:00.433 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
15:01:00.435 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
15:01:01.362 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
15:01:01.846 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
15:01:01.858 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.83 seconds (JVM running for 10.301)
15:01:01.861 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
15:01:01.861 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
15:01:01.861 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
15:01:01.861 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
15:01:01.863 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
15:01:01.863 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
15:01:01.863 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
15:01:01.863 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
15:01:01.863 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
15:01:01.863 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
15:01:02.487 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:05:59.025 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:05:59.024 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 19620 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
15:05:59.027 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
15:06:01.672 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
15:06:01.676 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
15:06:01.676 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
15:06:01.765 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
15:06:03.696 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
15:06:04.402 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
15:06:07.576 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:06:07.587 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:06:07.588 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:06:07.588 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
15:06:07.589 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

15:06:07.590 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
15:06:07.590 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:06:07.590 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@62e24c2a
15:06:07.686 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
15:06:07.687 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
15:06:07.690 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
15:06:08.549 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
15:06:08.885 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
15:06:08.894 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 10.209 seconds (JVM running for 10.69)
15:06:08.895 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
15:06:08.896 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
15:06:08.896 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
15:06:08.896 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
15:06:08.897 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
15:06:08.898 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
15:06:08.899 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
15:06:08.899 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
15:06:08.899 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
15:06:08.899 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
15:06:10.184 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:10:12.919 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 18840 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
15:10:12.922 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
15:10:12.924 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:10:15.404 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
15:10:15.407 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
15:10:15.408 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
15:10:15.497 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
15:10:17.361 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
15:10:18.083 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
15:10:21.157 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:10:21.169 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:10:21.169 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:10:21.170 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
15:10:21.173 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

15:10:21.173 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
15:10:21.173 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:10:21.173 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@faaa5d
15:10:21.265 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
15:10:21.266 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
15:10:21.269 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
15:10:22.130 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
15:10:22.458 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
15:10:22.467 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.892 seconds (JVM running for 10.353)
15:10:22.469 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
15:10:22.470 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
15:10:22.470 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
15:10:22.470 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
15:10:22.473 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
15:10:22.473 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
15:10:22.473 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
15:10:22.473 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
15:10:22.474 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
15:10:22.474 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
15:10:36.609 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:15:26.981 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:15:26.979 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 23256 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
15:15:26.983 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
15:15:29.491 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
15:15:29.494 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
15:15:29.494 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
15:15:29.596 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
15:15:31.463 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
15:15:32.228 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
15:15:35.291 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:15:35.300 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:15:35.301 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:15:35.301 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
15:15:35.302 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

15:15:35.303 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
15:15:35.303 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:15:35.303 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@ec1798
15:15:35.406 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
15:15:35.407 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
15:15:35.410 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
15:15:36.280 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
15:15:36.623 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
15:15:36.634 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.995 seconds (JVM running for 10.499)
15:15:36.636 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
15:15:36.636 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
15:15:36.636 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
15:15:36.636 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
15:15:36.637 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
15:15:36.638 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
15:15:36.638 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
15:15:36.638 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
15:15:36.639 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
15:15:36.640 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
15:16:52.542 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:19:59.028 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 20396 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
15:19:59.031 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
15:19:59.032 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:20:01.605 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
15:20:01.609 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
15:20:01.609 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
15:20:01.692 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
15:20:03.361 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
15:20:04.100 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
15:20:07.266 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:20:07.275 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:20:07.276 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:20:07.276 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
15:20:07.277 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

15:20:07.278 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
15:20:07.278 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:20:07.278 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@1a9fa5b3
15:20:07.367 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
15:20:07.367 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
15:20:07.370 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
15:20:08.282 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
15:20:08.688 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
15:20:08.697 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 10.012 seconds (JVM running for 10.498)
15:20:08.700 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
15:20:08.700 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
15:20:08.700 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
15:20:08.700 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
15:20:08.702 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
15:20:08.702 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
15:20:08.702 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
15:20:08.702 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
15:20:08.702 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
15:20:08.702 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
15:20:10.722 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:25:21.777 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 8136 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
15:25:21.782 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
15:25:21.782 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:25:24.367 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
15:25:24.371 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
15:25:24.371 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
15:25:24.455 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
15:25:26.198 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
15:25:26.941 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
15:25:30.106 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:25:30.117 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:25:30.118 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:25:30.119 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
15:25:30.119 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

15:25:30.120 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
15:25:30.121 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:25:30.121 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2d649cdb
15:25:30.224 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
15:25:30.225 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
15:25:30.229 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
15:25:31.199 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
15:25:31.538 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
15:25:31.547 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 10.113 seconds (JVM running for 10.615)
15:25:31.549 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
15:25:31.549 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
15:25:31.549 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
15:25:31.549 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
15:25:31.552 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
15:25:31.552 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
15:25:31.552 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
15:25:31.552 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
15:25:31.552 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
15:25:31.553 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
15:25:33.335 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:30:31.897 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 5584 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
15:30:31.901 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
15:30:31.905 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:30:34.504 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
15:30:34.506 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
15:30:34.507 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
15:30:34.595 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
15:30:36.414 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
15:30:37.092 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
15:30:40.178 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:30:40.189 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:30:40.189 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:30:40.190 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
15:30:40.190 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

15:30:40.190 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
15:30:40.191 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:30:40.191 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6560612f
15:30:40.286 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
15:30:40.287 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
15:30:40.290 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
15:30:41.158 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
15:30:41.504 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
15:30:41.515 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.967 seconds (JVM running for 10.449)
15:30:41.517 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
15:30:41.518 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
15:30:41.518 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
15:30:41.518 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
15:30:41.519 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
15:30:41.519 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
15:30:41.519 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
15:30:41.519 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
15:30:41.519 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
15:30:41.519 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
15:30:42.477 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:01:21.369 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 16812 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
16:01:21.373 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
16:01:21.377 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:01:23.925 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
16:01:23.928 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
16:01:23.929 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
16:01:24.011 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
16:01:25.844 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
16:01:26.550 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
16:01:30.120 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
16:01:30.131 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
16:01:30.131 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
16:01:30.132 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
16:01:30.133 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

16:01:30.133 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
16:01:30.134 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
16:01:30.134 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@61e87312
16:01:30.226 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
16:01:30.226 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
16:01:30.229 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
16:01:31.423 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
16:01:31.847 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
16:01:31.858 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 10.833 seconds (JVM running for 11.318)
16:01:31.861 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
16:01:31.861 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
16:01:31.862 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
16:01:31.862 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
16:01:31.863 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
16:01:31.863 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
16:01:31.863 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
16:01:31.863 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
16:01:31.863 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
16:01:31.864 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
16:01:44.265 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:02:02.907 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[fasuzhuguan][Logout][退出成功]
16:02:06.587 [schedule-pool-2] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
16:05:11.830 [schedule-pool-3] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Logout][退出成功]
16:05:24.331 [schedule-pool-2] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[daihouzhuguan][Success][登录成功]
16:05:46.387 [schedule-pool-4] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[daihouzhuguan][Logout][退出成功]
16:05:49.148 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Error][验证码错误]
16:05:52.600 [schedule-pool-5] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
16:07:29.011 [schedule-pool-6] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Logout][退出成功]
16:07:39.932 [schedule-pool-2] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[daihouzhuguan][Success][登录成功]
16:08:53.643 [schedule-pool-7] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[daihouzhuguan][Logout][退出成功]
16:08:57.839 [schedule-pool-4] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Error][验证码错误]
16:09:00.747 [schedule-pool-8] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
16:10:48.156 [schedule-pool-10] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Logout][退出成功]
16:10:50.728 [schedule-pool-3] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
16:11:02.620 [schedule-pool-11] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Logout][退出成功]
16:11:13.272 [schedule-pool-6] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[daihouwenyuan][Success][登录成功]
16:11:19.214 [schedule-pool-12] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[daihouwenyuan][Logout][退出成功]
16:11:28.609 [schedule-pool-2] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[daihouzhuguan][Success][登录成功]
16:19:15.935 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 22644 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
16:19:15.938 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
16:19:15.942 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:19:18.555 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
16:19:18.558 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
16:19:18.558 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
16:19:18.649 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
16:19:20.435 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
16:19:21.172 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
16:19:24.131 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
16:19:24.141 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
16:19:24.141 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
16:19:24.142 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
16:19:24.143 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

16:19:24.143 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
16:19:24.144 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
16:19:24.145 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@ec1798
16:19:24.248 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
16:19:24.248 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
16:19:24.251 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
16:19:25.129 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
16:19:25.465 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
16:19:25.476 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.879 seconds (JVM running for 10.366)
16:19:25.478 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
16:19:25.479 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
16:19:25.479 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
16:19:25.479 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
16:19:25.480 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
16:19:25.480 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
16:19:25.481 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
16:19:25.481 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
16:19:25.481 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
16:19:25.481 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
16:19:28.603 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:26:38.153 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 12868 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
16:26:38.155 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:26:38.156 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
16:26:40.620 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
16:26:40.623 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
16:26:40.623 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
16:26:40.715 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
16:26:42.383 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
16:26:43.105 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
16:26:46.183 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
16:26:46.192 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
16:26:46.192 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
16:26:46.193 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
16:26:46.194 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

16:26:46.194 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
16:26:46.194 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
16:26:46.195 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@61e87312
16:26:46.296 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
16:26:46.297 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
16:26:46.300 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
16:26:47.167 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
16:26:47.486 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
16:26:47.497 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.681 seconds (JVM running for 10.157)
16:26:47.501 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
16:26:47.501 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
16:26:47.502 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
16:26:47.502 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
16:26:47.503 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
16:26:47.503 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
16:26:47.503 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
16:26:47.503 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
16:26:47.503 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
16:26:47.503 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
16:29:58.876 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:30:08.656 [http-nio-8081-exec-11] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
