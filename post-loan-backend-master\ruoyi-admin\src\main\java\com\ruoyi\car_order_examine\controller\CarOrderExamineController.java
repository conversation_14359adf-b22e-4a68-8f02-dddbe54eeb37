package com.ruoyi.car_order_examine.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.car_order_examine.domain.CarOrderExamine;
import com.ruoyi.car_order_examine.service.ICarOrderExamineService;
import com.ruoyi.car_order_examine.utils.ApprovalStatusManager;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.Date;
import java.util.Map;
import java.util.HashMap;

/**
 * 找车费用审批Controller
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
@RestController
@RequestMapping("/car_order_examine/car_order_examine")
public class CarOrderExamineController extends BaseController
{
    @Autowired
    private ICarOrderExamineService carOrderExamineService;

    @Autowired
    private ISysUserService sysUserService;

    /**
     * 查询找车费用审批列表
     */
    @PreAuthorize("@ss.hasPermi('car_order_examine:car_order_examine:list')")
    @GetMapping("/list")
    public TableDataInfo list(CarOrderExamine carOrderExamine)
    {
        startPage();
        List<CarOrderExamine> list = carOrderExamineService.selectCarOrderExamineList(carOrderExamine);
        return getDataTable(list);
    }

    /**
     * 查询待审批列表
     */
    @PreAuthorize("@ss.hasPermi('car_order_examine:car_order_examine:list')")
    @GetMapping("/list/pending")
    public TableDataInfo listPending()
    {
        String currentUser = getUsername();
        String userRole = sysUserService.selectUserRoleGroup(currentUser);

        startPage();
        List<CarOrderExamine> list = carOrderExamineService.selectPendingApprovalList(userRole);
        return getDataTable(list);
    }

    /**
     * 导出找车费用审批列表
     */
    @PreAuthorize("@ss.hasPermi('car_order_examine:car_order_examine:export')")
    @Log(title = "找车费用审批", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CarOrderExamine carOrderExamine)
    {
        List<CarOrderExamine> list = carOrderExamineService.selectCarOrderExamineList(carOrderExamine);
        ExcelUtil<CarOrderExamine> util = new ExcelUtil<CarOrderExamine>(CarOrderExamine.class);
        util.exportExcel(response, list, "找车费用审批数据");
    }

    /**
     * 获取找车费用审批详细信息
     */
    @PreAuthorize("@ss.hasPermi('car_order_examine:car_order_examine:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(carOrderExamineService.selectCarOrderExamineById(id));
    }

    /**
     * 新增找车费用审批
     */
    @PreAuthorize("@ss.hasPermi('car_order_examine:car_order_examine:add')")
    @Log(title = "找车费用审批", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CarOrderExamine carOrderExamine)
    {
        return toAjax(carOrderExamineService.insertCarOrderExamine(carOrderExamine));
    }

    /**
     * 修改找车费用审批
     */
    @PreAuthorize("@ss.hasPermi('car_order_examine:car_order_examine:edit')")
    @Log(title = "找车费用审批", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CarOrderExamine carOrderExamine)
    {
        return toAjax(carOrderExamineService.updateCarOrderExamine(carOrderExamine));
    }

    /**
     * 删除找车费用审批
     */
    @PreAuthorize("@ss.hasPermi('car_order_examine:car_order_examine:remove')")
    @Log(title = "找车费用审批", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(carOrderExamineService.deleteCarOrderExamineByIds(ids));
    }

    /**
     * 审批找车费用申请
     */
    @PreAuthorize("@ss.hasPermi('car_order_examine:car_order_examine:approve')")
    @Log(title = "找车费用审批", businessType = BusinessType.UPDATE)
    @PutMapping("/approve")
    public AjaxResult approve(@RequestBody CarOrderExamine carOrderExamine)
    {
        if (carOrderExamine.getId() == null || carOrderExamine.getId().isEmpty()) {
            return error("审批ID不能为空");
        }

        // 获取当前用户角色
        String currentUser = getUsername();
        String userRole = sysUserService.selectUserRoleGroup(currentUser);

        // 获取原始审批信息
        CarOrderExamine existingExamine = carOrderExamineService.selectCarOrderExamineById(carOrderExamine.getId());
        if (existingExamine == null) {
            return error("找不到对应的审批信息");
        }

        // 检查是否可以审批
        if (!existingExamine.canApprove(userRole)) {
            return error("您没有权限进行当前阶段的审批，当前状态为【" + existingExamine.getStatusDescription() + "】");
        }

        // 检查审批结果
        Integer newStatus = carOrderExamine.getStatus();
        if (newStatus == null) {
            return error("请选择审批结果");
        }

        // 如果是拒绝，设置为拒绝状态
        if (newStatus == CarOrderExamine.STATUS_REJECTED) {
            if (carOrderExamine.getReasons() == null || carOrderExamine.getReasons().trim().isEmpty()) {
                return error("拒绝时必须提供拒绝原因");
            }
            existingExamine.setStatus(CarOrderExamine.STATUS_REJECTED);
            existingExamine.setReasons(carOrderExamine.getReasons());
        } else {
            // 通过审批，进入下一个状态
            Integer nextStatus = existingExamine.getNextApprovalStatus();
            existingExamine.setStatus(nextStatus);
            existingExamine.setReasons(null);
        }

        // 更新审批信息
        existingExamine.setApproveTime(new Date());
        existingExamine.setApproveBy(currentUser);
        existingExamine.setApproveRole(userRole);
        existingExamine.setUpdateBy(currentUser);
        existingExamine.setUpdateTime(new Date());

        // 记录审批历史
        String approvalRecord = String.format("[%s] %s %s - %s",
            new Date(), userRole, currentUser,
            existingExamine.getStatus() == CarOrderExamine.STATUS_REJECTED ?
                "拒绝：" + existingExamine.getReasons() : "通过");

        String currentHistory = existingExamine.getApprovalHistory();
        if (currentHistory == null || currentHistory.trim().isEmpty()) {
            existingExamine.setApprovalHistory(approvalRecord);
        } else {
            existingExamine.setApprovalHistory(currentHistory + "\n" + approvalRecord);
        }

        return toAjax(carOrderExamineService.updateCarOrderExamine(existingExamine));
    }

    /**
     * 开始审批流程
     */
    @PreAuthorize("@ss.hasPermi('car_order_examine:car_order_examine:approve')")
    @Log(title = "开始审批流程", businessType = BusinessType.UPDATE)
    @PostMapping("/startApproval/{id}")
    public AjaxResult startApprovalFlow(@PathVariable("id") String id)
    {
        if (id == null || id.isEmpty()) {
            return error("审批ID不能为空");
        }

        // 获取审批信息
        CarOrderExamine examine = carOrderExamineService.selectCarOrderExamineById(id);
        if (examine == null) {
            return error("找不到对应的审批信息");
        }

        // 检查当前状态是否可以开始审批流程
        if (examine.getStatus() != null && examine.getStatus() != CarOrderExamine.STATUS_PENDING) {
            return error("当前状态不允许开始审批流程，当前状态为【" + ApprovalStatusManager.getStatusText(examine.getStatus()) + "】");
        }

        // 设置为第一个审批节点
        examine.setStatus(ApprovalStatusManager.startApprovalFlow());
        examine.setUpdateBy(getUsername());
        examine.setUpdateTime(new Date());

        // 记录审批历史
        String approvalRecord = String.format("[%s] 系统 - 开始审批流程", new Date());
        examine.setApprovalHistory(approvalRecord);

        return toAjax(carOrderExamineService.updateCarOrderExamine(examine));
    }

    /**
     * 审批通过
     */
    @PreAuthorize("@ss.hasPermi('car_order_examine:car_order_examine:approve')")
    @Log(title = "审批通过", businessType = BusinessType.UPDATE)
    @PostMapping("/approve")
    public AjaxResult approveRecord(@RequestBody Map<String, Object> params)
    {
        String id = (String) params.get("id");
        Integer currentStatus = (Integer) params.get("currentStatus");

        if (id == null || id.isEmpty()) {
            return error("审批ID不能为空");
        }

        // 获取当前用户角色
        String currentUser = getUsername();
        String userRole = sysUserService.selectUserRoleGroup(currentUser);

        // 获取审批信息
        CarOrderExamine examine = carOrderExamineService.selectCarOrderExamineById(id);
        if (examine == null) {
            return error("找不到对应的审批信息");
        }

        // 验证当前状态
        if (!examine.getStatus().equals(currentStatus)) {
            return error("审批状态已发生变化，请刷新页面后重试");
        }

        // 检查是否可以审批
        if (!ApprovalStatusManager.canUserApprove(examine.getStatus(), userRole)) {
            return error("您没有权限进行当前阶段的审批，当前状态为【" + ApprovalStatusManager.getStatusText(examine.getStatus()) + "】，需要角色：" + ApprovalStatusManager.getRequiredRole(examine.getStatus()));
        }

        // 获取下一个状态
        Integer nextStatus = ApprovalStatusManager.handleApprove(examine.getStatus());
        examine.setStatus(nextStatus);
        examine.setApproveTime(new Date());
        examine.setApproveBy(currentUser);
        examine.setApproveRole(userRole);
        examine.setUpdateBy(currentUser);
        examine.setUpdateTime(new Date());
        examine.setReasons(null); // 清除拒绝原因

        // 记录审批历史
        String approvalRecord = String.format("[%s] %s %s - 审批通过", new Date(), userRole, currentUser);
        String currentHistory = examine.getApprovalHistory();
        if (currentHistory == null || currentHistory.trim().isEmpty()) {
            examine.setApprovalHistory(approvalRecord);
        } else {
            examine.setApprovalHistory(currentHistory + "\n" + approvalRecord);
        }

        return toAjax(carOrderExamineService.updateCarOrderExamine(examine));
    }

    /**
     * 审批拒绝
     */
    @PreAuthorize("@ss.hasPermi('car_order_examine:car_order_examine:approve')")
    @Log(title = "审批拒绝", businessType = BusinessType.UPDATE)
    @PostMapping("/reject")
    public AjaxResult rejectRecord(@RequestBody Map<String, Object> params)
    {
        String id = (String) params.get("id");
        Integer currentStatus = (Integer) params.get("currentStatus");
        String rejectReason = (String) params.get("rejectReason");

        if (id == null || id.isEmpty()) {
            return error("审批ID不能为空");
        }

        if (rejectReason == null || rejectReason.trim().isEmpty()) {
            return error("拒绝原因不能为空");
        }

        // 获取当前用户角色
        String currentUser = getUsername();
        String userRole = sysUserService.selectUserRoleGroup(currentUser);

        // 获取审批信息
        CarOrderExamine examine = carOrderExamineService.selectCarOrderExamineById(id);
        if (examine == null) {
            return error("找不到对应的审批信息");
        }

        // 验证当前状态
        if (!examine.getStatus().equals(currentStatus)) {
            return error("审批状态已发生变化，请刷新页面后重试");
        }

        // 检查是否可以审批
        if (!ApprovalStatusManager.canUserApprove(examine.getStatus(), userRole)) {
            return error("您没有权限进行当前阶段的审批，当前状态为【" + ApprovalStatusManager.getStatusText(examine.getStatus()) + "】，需要角色：" + ApprovalStatusManager.getRequiredRole(examine.getStatus()));
        }

        // 设置为拒绝状态
        examine.setStatus(ApprovalStatusManager.handleReject(examine.getStatus()));
        examine.setReasons(rejectReason);
        examine.setApproveTime(new Date());
        examine.setApproveBy(currentUser);
        examine.setApproveRole(userRole);
        examine.setUpdateBy(currentUser);
        examine.setUpdateTime(new Date());

        // 记录审批历史
        String approvalRecord = String.format("[%s] %s %s - 审批拒绝：%s", new Date(), userRole, currentUser, rejectReason);
        String currentHistory = examine.getApprovalHistory();
        if (currentHistory == null || currentHistory.trim().isEmpty()) {
            examine.setApprovalHistory(approvalRecord);
        } else {
            examine.setApprovalHistory(currentHistory + "\n" + approvalRecord);
        }

        return toAjax(carOrderExamineService.updateCarOrderExamine(examine));
    }

    /**
     * 根据订单ID获取费用记录列表
     */
    @PreAuthorize("@ss.hasPermi('car_order_examine:car_order_examine:query')")
    @GetMapping("/records/{orderId}")
    public AjaxResult getRecordsByOrderId(@PathVariable("orderId") String orderId)
    {
        List<CarOrderExamine> records = carOrderExamineService.selectCarOrderExamineListByOrderId(orderId);
        return success(records);
    }

    /**
     * 批量审批费用记录
     */
    @PreAuthorize("@ss.hasPermi('car_order_examine:car_order_examine:approve')")
    @Log(title = "批量审批", businessType = BusinessType.UPDATE)
    @PostMapping("/batchApprove")
    public AjaxResult batchApprove(@RequestBody Map<String, Object> params)
    {
        @SuppressWarnings("unchecked")
        List<String> ids = (List<String>) params.get("ids");
        String action = (String) params.get("action");
        String rejectReason = (String) params.get("rejectReason");

        if (ids == null || ids.isEmpty()) {
            return error("请选择要审批的记录");
        }

        if (!"approve".equals(action) && !"reject".equals(action)) {
            return error("无效的审批操作");
        }

        if ("reject".equals(action) && (rejectReason == null || rejectReason.trim().isEmpty())) {
            return error("拒绝审批时必须填写拒绝原因");
        }

        // 获取当前用户信息
        String currentUser = getUsername();
        String userRole = sysUserService.selectUserRoleGroup(currentUser);

        int successCount = 0;
        int failCount = 0;
        StringBuilder errorMessages = new StringBuilder();

        for (String id : ids) {
            try {
                CarOrderExamine examine = carOrderExamineService.selectCarOrderExamineById(id);
                if (examine == null) {
                    failCount++;
                    errorMessages.append("ID为").append(id).append("的记录不存在；");
                    continue;
                }

                // 检查是否可以审批
                if (!ApprovalStatusManager.canUserApprove(examine.getStatus(), userRole)) {
                    failCount++;
                    errorMessages.append("ID为").append(id).append("的记录当前状态不允许审批；");
                    continue;
                }

                // 执行审批操作
                if ("approve".equals(action)) {
                    examine.setStatus(ApprovalStatusManager.handleApprove(examine.getStatus()));
                } else {
                    examine.setStatus(ApprovalStatusManager.handleReject(examine.getStatus()));
                    examine.setReasons(rejectReason);
                }

                examine.setApproveTime(new Date());
                examine.setApproveBy(currentUser);
                examine.setApproveRole(userRole);
                examine.setUpdateBy(currentUser);
                examine.setUpdateTime(new Date());

                // 记录审批历史
                String approvalRecord = String.format("[%s] %s %s - 批量%s",
                    new Date(), userRole, currentUser, "approve".equals(action) ? "审批通过" : "审批拒绝：" + rejectReason);
                String currentHistory = examine.getApprovalHistory();
                if (currentHistory == null || currentHistory.trim().isEmpty()) {
                    examine.setApprovalHistory(approvalRecord);
                } else {
                    examine.setApprovalHistory(currentHistory + "\n" + approvalRecord);
                }

                carOrderExamineService.updateCarOrderExamine(examine);
                successCount++;

            } catch (Exception e) {
                failCount++;
                errorMessages.append("ID为").append(id).append("的记录审批失败：").append(e.getMessage()).append("；");
            }
        }

        if (failCount == 0) {
            return success("批量审批成功，共处理 " + successCount + " 条记录");
        } else if (successCount == 0) {
            return error("批量审批失败：" + errorMessages.toString());
        } else {
            return success("批量审批完成，成功 " + successCount + " 条，失败 " + failCount + " 条。失败原因：" + errorMessages.toString());
        }
    }

    /**
     * 单个审批费用记录
     */
    @PreAuthorize("@ss.hasPermi('car_order_examine:car_order_examine:approve')")
    @Log(title = "单个审批", businessType = BusinessType.UPDATE)
    @PostMapping("/singleApprove")
    public AjaxResult singleApprove(@RequestBody Map<String, Object> params)
    {
        String id = (String) params.get("id");
        String action = (String) params.get("action");
        String rejectReason = (String) params.get("rejectReason");

        if (id == null || id.isEmpty()) {
            return error("审批ID不能为空");
        }

        if (!"approve".equals(action) && !"reject".equals(action)) {
            return error("无效的审批操作");
        }

        if ("reject".equals(action) && (rejectReason == null || rejectReason.trim().isEmpty())) {
            return error("拒绝审批时必须填写拒绝原因");
        }

        // 获取当前用户信息
        String currentUser = getUsername();
        String userRole = sysUserService.selectUserRoleGroup(currentUser);

        try {
            CarOrderExamine examine = carOrderExamineService.selectCarOrderExamineById(id);
            if (examine == null) {
                return error("找不到对应的审批记录");
            }

            // 添加调试日志
            System.out.println("=== 审批调试信息 ===");
            System.out.println("记录ID: " + id);
            System.out.println("当前状态: " + examine.getStatus());
            System.out.println("用户角色: " + userRole);
            System.out.println("操作类型: " + action);
            System.out.println("canApprove: " + ApprovalStatusManager.canApprove(examine.getStatus()));
            System.out.println("canUserApprove: " + ApprovalStatusManager.canUserApprove(examine.getStatus(), userRole));

            // 检查是否可以审批
            if (!ApprovalStatusManager.canUserApprove(examine.getStatus(), userRole)) {
                return error("您没有权限进行当前阶段的审批，当前状态为【" + ApprovalStatusManager.getStatusText(examine.getStatus()) + "】，需要角色：" + ApprovalStatusManager.getRequiredRole(examine.getStatus()) + "，您的角色：" + userRole);
            }

            // 执行审批操作
            try {
                if ("approve".equals(action)) {
                    examine.setStatus(ApprovalStatusManager.handleApprove(examine.getStatus()));
                } else {
                    examine.setStatus(ApprovalStatusManager.handleReject(examine.getStatus()));
                    examine.setReasons(rejectReason);
                }
            } catch (IllegalStateException e) {
                return error("审批操作失败：" + e.getMessage() + "，当前状态：" + examine.getStatus() + "，用户角色：" + userRole);
            }

            examine.setApproveTime(new Date());
            examine.setApproveBy(currentUser);
            examine.setApproveRole(userRole);
            examine.setUpdateBy(currentUser);
            examine.setUpdateTime(new Date());

            // 记录审批历史
            String approvalRecord = String.format("[%s] %s %s - %s",
                new Date(), userRole, currentUser, "approve".equals(action) ? "审批通过" : "审批拒绝：" + rejectReason);
            String currentHistory = examine.getApprovalHistory();
            if (currentHistory == null || currentHistory.trim().isEmpty()) {
                examine.setApprovalHistory(approvalRecord);
            } else {
                examine.setApprovalHistory(currentHistory + "\n" + approvalRecord);
            }

            carOrderExamineService.updateCarOrderExamine(examine);
            return success("审批成功");

        } catch (Exception e) {
            return error("审批失败：" + e.getMessage());
        }
    }
}
