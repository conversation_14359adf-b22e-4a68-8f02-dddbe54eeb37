package com.ruoyi.litigation_cost_approval.utils;

/**
 * 诉讼费用审批流程状态管理工具类
 * 
 * 审批状态定义：
 * 0: 未审批
 * 1: 全部同意（最终完成状态）
 * 2: 已拒绝（最终拒绝状态）
 * 3: 法诉主管审批（当前审批节点）
 * 4: 总监审批（当前审批节点）
 * 5: 财务主管/总监抄送（当前审批节点）
 * 6: 总经理/董事长审批(抄送)（当前审批节点）
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
public class LitigationApprovalStatusManager {

    // 审批状态常量
    public static final String STATUS_PENDING = "0";        // 未审批
    public static final String STATUS_APPROVED = "1";       // 全部同意
    public static final String STATUS_REJECTED = "2";       // 已拒绝
    public static final String STATUS_LEGAL_SUPERVISOR = "3";  // 法诉主管审批
    public static final String STATUS_DIRECTOR = "4";       // 总监审批
    public static final String STATUS_DIRECTOR_CC = "5";    // 财务主管/总监抄送
    public static final String STATUS_GENERAL_MANAGER = "6"; // 总经理/董事长审批(抄送)

    // 审批流程顺序
    private static final String[] APPROVAL_FLOW = {
        STATUS_LEGAL_SUPERVISOR,   // 法诉主管审批
        STATUS_DIRECTOR,           // 总监审批
        STATUS_DIRECTOR_CC,        // 财务主管/总监抄送
        STATUS_GENERAL_MANAGER     // 总经理/董事长审批
    };

    /**
     * 获取下一个审批状态
     * @param currentStatus 当前状态
     * @return 下一个状态，如果没有下一个状态返回null
     */
    public static String getNextStatus(String currentStatus) {
        if (currentStatus == null) {
            return null;
        }

        for (int i = 0; i < APPROVAL_FLOW.length; i++) {
            if (APPROVAL_FLOW[i].equals(currentStatus)) {
                if (i == APPROVAL_FLOW.length - 1) {
                    // 最后一个节点，返回全部同意
                    return STATUS_APPROVED;
                } else {
                    // 返回下一个节点
                    return APPROVAL_FLOW[i + 1];
                }
            }
        }
        return null;
    }

    /**
     * 检查是否为最后一个审批节点
     * @param status 当前状态
     * @return 是否为最后一个审批节点
     */
    public static boolean isLastApprovalNode(String status) {
        return status != null && status.equals(STATUS_GENERAL_MANAGER);
    }

    /**
     * 检查是否为最终状态（已完成或已拒绝）
     * @param status 状态
     * @return 是否为最终状态
     */
    public static boolean isFinalStatus(String status) {
        return status != null && 
               (status.equals(STATUS_APPROVED) || status.equals(STATUS_REJECTED));
    }

    /**
     * 检查是否可以进行审批操作
     * @param status 当前状态
     * @return 是否可以审批
     */
    public static boolean canApprove(String status) {
        // 空状态（null或空字符串）可以进行审批操作
        if (status == null || status.trim().isEmpty()) {
            return true;
        }

        // 未审批状态也可以进行审批操作
        if (STATUS_PENDING.equals(status)) {
            return true;
        }

        // 检查是否在审批流程中
        for (String approvalStatus : APPROVAL_FLOW) {
            if (approvalStatus.equals(status)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 处理审批通过
     * @param currentStatus 当前状态
     * @return 新状态
     * @throws IllegalStateException 当前状态不允许审批操作
     */
    public static String handleApprove(String currentStatus) {
        if (!canApprove(currentStatus)) {
            throw new IllegalStateException("当前状态不允许审批操作");
        }

        // 如果是空状态或未审批状态，进入第一个审批节点
        if (currentStatus == null || currentStatus.trim().isEmpty() || STATUS_PENDING.equals(currentStatus)) {
            return APPROVAL_FLOW[0]; // 进入法诉主管审批状态 "3"
        }

        // 如果是最后一个审批节点，设置为全部同意
        if (isLastApprovalNode(currentStatus)) {
            return STATUS_APPROVED;
        }

        // 否则进入下一个审批节点
        String nextStatus = getNextStatus(currentStatus);
        if (nextStatus == null) {
            throw new IllegalStateException("无法获取下一个审批状态");
        }

        return nextStatus;
    }

    /**
     * 处理审批拒绝
     * @param currentStatus 当前状态
     * @return 新状态（始终为已拒绝）
     * @throws IllegalStateException 当前状态不允许审批操作
     */
    public static String handleReject(String currentStatus) {
        if (!canApprove(currentStatus)) {
            throw new IllegalStateException("当前状态不允许审批操作");
        }
        return STATUS_REJECTED;
    }

    /**
     * 开始审批流程
     * @return 第一个审批节点状态
     */
    public static String startApprovalFlow() {
        return APPROVAL_FLOW[0];
    }

    /**
     * 获取状态描述文本
     * @param status 状态码
     * @return 状态描述
     */
    public static String getStatusText(String status) {
        // 空状态视为未审批
        if (status == null || status.trim().isEmpty()) {
            return "未审批";
        }

        switch (status) {
            case STATUS_PENDING:
                return "未审批";
            case STATUS_APPROVED:
                return "全部同意";
            case STATUS_REJECTED:
                return "已拒绝";
            case STATUS_LEGAL_SUPERVISOR:
                return "法诉主管审批";
            case STATUS_DIRECTOR:
                return "总监审批";
            case STATUS_DIRECTOR_CC:
                return "财务主管/总监抄送";
            case STATUS_GENERAL_MANAGER:
                return "总经理/董事长审批";
            default:
                return "未知状态";
        }
    }

    /**
     * 验证状态转换是否合法
     * @param fromStatus 原状态
     * @param toStatus 目标状态
     * @return 是否合法
     */
    public static boolean isValidStatusTransition(String fromStatus, String toStatus) {
        if (fromStatus == null || toStatus == null) {
            return false;
        }

        // 已完成或已拒绝的状态不能再转换
        if (isFinalStatus(fromStatus)) {
            return false;
        }

        // 只能转换为拒绝状态或下一个审批节点
        if (toStatus.equals(STATUS_REJECTED)) {
            return canApprove(fromStatus);
        }

        // 检查是否为合法的下一个状态
        if (isLastApprovalNode(fromStatus)) {
            return toStatus.equals(STATUS_APPROVED);
        }

        return toStatus.equals(getNextStatus(fromStatus));
    }

    /**
     * 根据用户角色检查是否可以审批当前状态
     * @param status 当前状态
     * @param userRole 用户角色（可能是逗号分隔的多个角色）
     * @return 是否可以审批
     */
    public static boolean canUserApprove(String status, String userRole) {
        if (userRole == null) {
            return false;
        }

        // 处理多角色情况（逗号分隔）
        String[] roles = userRole.split(",");

        // 处理空状态（null或空字符串）- 需要法诉主管审批
        if (status == null || status.trim().isEmpty()) {
            return hasRole(roles, "法诉主管") || hasRole(roles, "litigation_supervisor") || hasRole(roles, "judicial_director");
        }

        switch (status) {
            case STATUS_PENDING:
                return hasRole(roles, "法诉主管") || hasRole(roles, "litigation_supervisor") || hasRole(roles, "judicial_director");
            case STATUS_LEGAL_SUPERVISOR:
                // 法诉主管审批状态，应该由总监来审批
                return hasRole(roles, "总监") || hasRole(roles, "director");
            case STATUS_DIRECTOR:
                // 总监审批状态，应该由财务主管/总监来审批
                return hasRole(roles, "财务主管") || hasRole(roles, "财务总监") ||
                       hasRole(roles, "finance_supervisor") || hasRole(roles, "finance_director");
            case STATUS_DIRECTOR_CC:
                // 财务主管/总监抄送状态，应该由总经理/董事长来审批
                return hasRole(roles, "总经理") || hasRole(roles, "董事长") ||
                       hasRole(roles, "general_manager") || hasRole(roles, "chairman");
            case STATUS_GENERAL_MANAGER:
                // 总经理/董事长审批状态，已经是最后一级，不应该再有人审批
                return false;
            default:
                return false;
        }
    }

    /**
     * 检查角色数组中是否包含指定角色
     * @param roles 角色数组
     * @param targetRole 目标角色
     * @return 是否包含
     */
    private static boolean hasRole(String[] roles, String targetRole) {
        for (String role : roles) {
            if (targetRole.equals(role.trim())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取当前状态需要的审批角色
     * @param status 当前状态
     * @return 需要的审批角色
     */
    public static String getRequiredRole(String status) {
        // 空状态需要法诉主管审批
        if (status == null || status.trim().isEmpty()) {
            return "法诉主管";
        }

        switch (status) {
            case STATUS_PENDING:
                return "法诉主管";
            case STATUS_LEGAL_SUPERVISOR:
                return "总监";
            case STATUS_DIRECTOR:
                return "财务主管/总监";
            case STATUS_DIRECTOR_CC:
                return "总经理/董事长";
            case STATUS_GENERAL_MANAGER:
                return "已完成";
            default:
                return "未知";
        }
    }
}
