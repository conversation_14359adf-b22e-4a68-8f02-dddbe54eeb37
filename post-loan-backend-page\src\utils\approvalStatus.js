/**
 * 审批流程状态管理系统
 * 
 * 审批状态定义：
 * 0: 未审批
 * 1: 全部同意（最终完成状态）
 * 2: 已拒绝（最终拒绝状态）
 * 3: 贷后主管审批（当前审批节点）
 * 4: 总监审批（当前审批节点）
 * 5: 财务主管/总监抄送（当前审批节点）
 * 6: 总经理/董事长审批(抄送)（当前审批节点）
 */

// 审批状态常量
export const APPROVAL_STATUS = {
  PENDING: 0,           // 未审批
  APPROVED: 1,          // 全部同意
  REJECTED: 2,          // 已拒绝
  LEGAL_SUPERVISOR: 3,  // 贷后主管审批
  DIRECTOR: 4,          // 总监审批
  FINANCE_SUPERVISOR: 5, // 财务主管/总监抄送
  GENERAL_MANAGER: 6    // 总经理/董事长审批
}

// 审批状态描述
export const APPROVAL_STATUS_TEXT = {
  [APPROVAL_STATUS.PENDING]: '未审批',
  [APPROVAL_STATUS.APPROVED]: '全部同意',
  [APPROVAL_STATUS.REJECTED]: '已拒绝',
  [APPROVAL_STATUS.LEGAL_SUPERVISOR]: '贷后主管审批',
  [APPROVAL_STATUS.DIRECTOR]: '总监审批',
  [APPROVAL_STATUS.FINANCE_SUPERVISOR]: '财务主管/总监抄送',
  [APPROVAL_STATUS.GENERAL_MANAGER]: '总经理/董事长审批'
}

// 审批流程顺序
export const APPROVAL_FLOW = [
  APPROVAL_STATUS.LEGAL_SUPERVISOR,   // 贷后主管审批
  APPROVAL_STATUS.DIRECTOR,           // 总监审批
  APPROVAL_STATUS.FINANCE_SUPERVISOR, // 财务主管/总监抄送
  APPROVAL_STATUS.GENERAL_MANAGER     // 总经理/董事长审批
]

/**
 * 审批流程管理类
 */
export class ApprovalManager {
  
  /**
   * 获取下一个审批状态
   * @param {number} currentStatus 当前状态
   * @returns {number|null} 下一个状态，如果没有下一个状态返回null
   */
  static getNextStatus(currentStatus) {
    const currentIndex = APPROVAL_FLOW.indexOf(currentStatus)
    if (currentIndex === -1 || currentIndex === APPROVAL_FLOW.length - 1) {
      return null
    }
    return APPROVAL_FLOW[currentIndex + 1]
  }

  /**
   * 检查是否为最后一个审批节点
   * @param {number} status 当前状态
   * @returns {boolean}
   */
  static isLastApprovalNode(status) {
    return status === APPROVAL_STATUS.GENERAL_MANAGER
  }

  /**
   * 检查是否为最终状态（已完成或已拒绝）
   * @param {number} status 状态
   * @returns {boolean}
   */
  static isFinalStatus(status) {
    return status === APPROVAL_STATUS.APPROVED || status === APPROVAL_STATUS.REJECTED
  }

  /**
   * 检查是否可以进行审批操作
   * @param {number} status 当前状态
   * @returns {boolean}
   */
  static canApprove(status) {
    return APPROVAL_FLOW.includes(status)
  }

  /**
   * 处理审批通过
   * @param {number} currentStatus 当前状态
   * @returns {number} 新状态
   */
  static handleApprove(currentStatus) {
    if (!this.canApprove(currentStatus)) {
      throw new Error('当前状态不允许审批操作')
    }

    // 如果是最后一个审批节点，设置为全部同意
    if (this.isLastApprovalNode(currentStatus)) {
      return APPROVAL_STATUS.APPROVED
    }

    // 否则进入下一个审批节点
    const nextStatus = this.getNextStatus(currentStatus)
    if (nextStatus === null) {
      throw new Error('无法获取下一个审批状态')
    }

    return nextStatus
  }

  /**
   * 处理审批拒绝
   * @param {number} currentStatus 当前状态
   * @returns {number} 新状态（始终为已拒绝）
   */
  static handleReject(currentStatus) {
    if (!this.canApprove(currentStatus)) {
      throw new Error('当前状态不允许审批操作')
    }
    return APPROVAL_STATUS.REJECTED
  }

  /**
   * 开始审批流程
   * @returns {number} 第一个审批节点状态
   */
  static startApprovalFlow() {
    return APPROVAL_FLOW[0]
  }

  /**
   * 获取状态描述文本
   * @param {number} status 状态码
   * @returns {string} 状态描述
   */
  static getStatusText(status) {
    return APPROVAL_STATUS_TEXT[status] || '未知状态'
  }

  /**
   * 获取状态对应的标签类型（用于UI显示）
   * @param {number} status 状态码
   * @returns {string} 标签类型
   */
  static getStatusTagType(status) {
    switch (status) {
      case APPROVAL_STATUS.PENDING:
        return 'info'
      case APPROVAL_STATUS.APPROVED:
        return 'success'
      case APPROVAL_STATUS.REJECTED:
        return 'danger'
      case APPROVAL_STATUS.LEGAL_SUPERVISOR:
      case APPROVAL_STATUS.DIRECTOR:
      case APPROVAL_STATUS.FINANCE_SUPERVISOR:
      case APPROVAL_STATUS.GENERAL_MANAGER:
        return 'warning'
      default:
        return 'info'
    }
  }

  /**
   * 验证状态转换是否合法
   * @param {number} fromStatus 原状态
   * @param {number} toStatus 目标状态
   * @returns {boolean} 是否合法
   */
  static isValidStatusTransition(fromStatus, toStatus) {
    // 已完成或已拒绝的状态不能再转换
    if (this.isFinalStatus(fromStatus)) {
      return false
    }

    // 只能转换为拒绝状态或下一个审批节点
    if (toStatus === APPROVAL_STATUS.REJECTED) {
      return this.canApprove(fromStatus)
    }

    // 检查是否为合法的下一个状态
    if (this.isLastApprovalNode(fromStatus)) {
      return toStatus === APPROVAL_STATUS.APPROVED
    }

    return toStatus === this.getNextStatus(fromStatus)
  }
}

export default ApprovalManager
