package com.ruoyi.litigation_cost_approval.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 法诉费用审批对象 litigation_cost_approval
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public class LitigationCostApproval extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    // 审批状态常量
    public static final String STATUS_PENDING = "0";        // 未审批
    public static final String STATUS_APPROVED = "1";       // 全部同意
    public static final String STATUS_REJECTED = "2";       // 已拒绝
    public static final String STATUS_LEGAL_SUPERVISOR = "3";  // 法诉主管审批
    public static final String STATUS_DIRECTOR = "4";       // 总监审批
    public static final String STATUS_DIRECTOR_CC = "5";    // 财务主管/总监抄送
    public static final String STATUS_GENERAL_MANAGER = "6"; // 总经理/董事长审批(抄送)

    /** 主键ID */
    private Long id;

    /** 关联法诉案件ID */
    @Excel(name = "关联法诉案件ID")
    private Long litigationCaseId;

    /** 律师费 */
    @Excel(name = "律师费")
    private BigDecimal lawyerFee;

    /** 诉讼费 */
    @Excel(name = "诉讼费")
    private BigDecimal litigationFee;

    /** 保全费 */
    @Excel(name = "保全费")
    private BigDecimal preservationFee;

    /** 布控费 */
    @Excel(name = "布控费")
    private BigDecimal surveillanceFee;

    /** 公告费 */
    @Excel(name = "公告费")
    private BigDecimal announcementFee;

    /** 评估费 */
    @Excel(name = "评估费")
    private BigDecimal appraisalFee;

    /** 执行费 */
    @Excel(name = "执行费")
    private BigDecimal executionFee;

    /** 违约金 */
    @Excel(name = "违约金")
    private BigDecimal penalty;

    /** 担保费 */
    @Excel(name = "担保费")
    private BigDecimal guaranteeFee;

    /** 居间费 */
    @Excel(name = "居间费")
    private BigDecimal intermediaryFee;

    /** 代偿金 */
    @Excel(name = "代偿金")
    private BigDecimal compensity;

    /** 判决金额 */
    @Excel(name = "判决金额")
    private BigDecimal judgmentAmount;

    /** 特殊通道费 */
    @Excel(name = "特殊通道费")
    private BigDecimal specialChannelFees;

    /** 利息 */
    @Excel(name = "利息")
    private BigDecimal interest;

    /** 其他欠款 */
    @Excel(name = "其他欠款")
    private BigDecimal otherAmountsOwed;

    /** 保险费 */
    @Excel(name = "保险费")
    private BigDecimal insurance;

    /** 审批状态(0-未审批   1-全部同意  2-已拒绝  3-法诉主管审批  4-总监审批  5-财务主管/总监抄送  6-总经理/董事长审批(抄送)) */
    @Excel(name = "审批状态")
    private String approvalStatus;

    /** 拒绝原因 */
    @Excel(name = "拒绝原因")
    private String reasons;

    /** 审批人 */
    @Excel(name = "审批人")
    private String approveBy;

    /** 审批人角色 */
    @Excel(name = "审批人角色")
    private String approveRole;

    /** 申请时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "申请时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date applicationTime;

    /** 申请人 */
    @Excel(name = "申请人")
    private String applicationBy;

    /** 总费用 */
    @Excel(name = "总费用")
    private BigDecimal totalMoney;

    /** 提交次数 */
    @Excel(name = "提交次数")
    private Integer submissionCount;

    /** 费用类别 */
    @Excel(name = "费用类别")
    private String costCategory;

    /** 审批时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审批时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date approveTime;

    // 关联查询字段
    /** 贷款人姓名 */
    private String customerName;

    /** 客户编号 */
    private String customerId;

    /** 申请编号 */
    private String applyId;

    /** 身份证号 */
    private String certId;

    /** 案件负责人 */
    private String curator;

    /** 法院地 */
    private String courtLocation;

    /** 诉讼法院 */
    private String commonPleas;

    /** 出单渠道 */
    private String jgName;

    /** 地区 */
    private String area;

    /** 放款银行 */
    private String lendingBank;

    /** 法诉状态 */
    private String litigationStatus;

    /** 整体审批状态 */
    private String overallApprovalStatus;

    // 查询条件字段
    /** 开始时间 */
    private String startTime;

    /** 结束时间 */
    private String endTime;

    /** 审批开始时间 */
    private String approvalStartTime;

    /** 审批结束时间 */
    private String approvalEndTime;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        // 如果id为空，返回litigationCaseId作为主键
        return id != null ? id : litigationCaseId;
    }

    public void setLitigationCaseId(Long litigationCaseId) 
    {
        this.litigationCaseId = litigationCaseId;
    }

    public Long getLitigationCaseId() 
    {
        return litigationCaseId;
    }

    public void setLawyerFee(BigDecimal lawyerFee) 
    {
        this.lawyerFee = lawyerFee;
    }

    public BigDecimal getLawyerFee() 
    {
        return lawyerFee;
    }

    public void setLitigationFee(BigDecimal litigationFee) 
    {
        this.litigationFee = litigationFee;
    }

    public BigDecimal getLitigationFee() 
    {
        return litigationFee;
    }

    public void setPreservationFee(BigDecimal preservationFee) 
    {
        this.preservationFee = preservationFee;
    }

    public BigDecimal getPreservationFee() 
    {
        return preservationFee;
    }

    public void setSurveillanceFee(BigDecimal surveillanceFee) 
    {
        this.surveillanceFee = surveillanceFee;
    }

    public BigDecimal getSurveillanceFee() 
    {
        return surveillanceFee;
    }

    public void setAnnouncementFee(BigDecimal announcementFee) 
    {
        this.announcementFee = announcementFee;
    }

    public BigDecimal getAnnouncementFee() 
    {
        return announcementFee;
    }

    public void setAppraisalFee(BigDecimal appraisalFee) 
    {
        this.appraisalFee = appraisalFee;
    }

    public BigDecimal getAppraisalFee() 
    {
        return appraisalFee;
    }

    public void setExecutionFee(BigDecimal executionFee) 
    {
        this.executionFee = executionFee;
    }

    public BigDecimal getExecutionFee() 
    {
        return executionFee;
    }

    public void setPenalty(BigDecimal penalty) 
    {
        this.penalty = penalty;
    }

    public BigDecimal getPenalty() 
    {
        return penalty;
    }

    public void setGuaranteeFee(BigDecimal guaranteeFee) 
    {
        this.guaranteeFee = guaranteeFee;
    }

    public BigDecimal getGuaranteeFee() 
    {
        return guaranteeFee;
    }

    public void setIntermediaryFee(BigDecimal intermediaryFee) 
    {
        this.intermediaryFee = intermediaryFee;
    }

    public BigDecimal getIntermediaryFee() 
    {
        return intermediaryFee;
    }

    public void setCompensity(BigDecimal compensity) 
    {
        this.compensity = compensity;
    }

    public BigDecimal getCompensity() 
    {
        return compensity;
    }

    public void setJudgmentAmount(BigDecimal judgmentAmount) 
    {
        this.judgmentAmount = judgmentAmount;
    }

    public BigDecimal getJudgmentAmount() 
    {
        return judgmentAmount;
    }

    public void setSpecialChannelFees(BigDecimal specialChannelFees) 
    {
        this.specialChannelFees = specialChannelFees;
    }

    public BigDecimal getSpecialChannelFees() 
    {
        return specialChannelFees;
    }

    public void setInterest(BigDecimal interest) 
    {
        this.interest = interest;
    }

    public BigDecimal getInterest() 
    {
        return interest;
    }

    public void setOtherAmountsOwed(BigDecimal otherAmountsOwed) 
    {
        this.otherAmountsOwed = otherAmountsOwed;
    }

    public BigDecimal getOtherAmountsOwed() 
    {
        return otherAmountsOwed;
    }

    public void setInsurance(BigDecimal insurance) 
    {
        this.insurance = insurance;
    }

    public BigDecimal getInsurance() 
    {
        return insurance;
    }

    public void setApprovalStatus(String approvalStatus) 
    {
        this.approvalStatus = approvalStatus;
    }

    public String getApprovalStatus() 
    {
        return approvalStatus;
    }

    public void setReasons(String reasons) 
    {
        this.reasons = reasons;
    }

    public String getReasons() 
    {
        return reasons;
    }

    public void setApproveBy(String approveBy) 
    {
        this.approveBy = approveBy;
    }

    public String getApproveBy() 
    {
        return approveBy;
    }

    public void setApproveRole(String approveRole) 
    {
        this.approveRole = approveRole;
    }

    public String getApproveRole() 
    {
        return approveRole;
    }

    public void setApplicationTime(Date applicationTime) 
    {
        this.applicationTime = applicationTime;
    }

    public Date getApplicationTime() 
    {
        return applicationTime;
    }

    public void setApplicationBy(String applicationBy) 
    {
        this.applicationBy = applicationBy;
    }

    public String getApplicationBy() 
    {
        return applicationBy;
    }

    public void setTotalMoney(BigDecimal totalMoney) 
    {
        this.totalMoney = totalMoney;
    }

    public BigDecimal getTotalMoney() 
    {
        return totalMoney;
    }

    public void setSubmissionCount(Integer submissionCount) 
    {
        this.submissionCount = submissionCount;
    }

    public Integer getSubmissionCount() 
    {
        return submissionCount;
    }

    public void setCostCategory(String costCategory) 
    {
        this.costCategory = costCategory;
    }

    public String getCostCategory() 
    {
        return costCategory;
    }

    public void setApproveTime(Date approveTime) 
    {
        this.approveTime = approveTime;
    }

    public Date getApproveTime() 
    {
        return approveTime;
    }

    // 关联查询字段的getter和setter
    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getCertId() {
        return certId;
    }

    public void setCertId(String certId) {
        this.certId = certId;
    }

    public String getCurator() {
        return curator;
    }

    public void setCurator(String curator) {
        this.curator = curator;
    }

    public String getCourtLocation() {
        return courtLocation;
    }

    public void setCourtLocation(String courtLocation) {
        this.courtLocation = courtLocation;
    }

    public String getCommonPleas() {
        return commonPleas;
    }

    public void setCommonPleas(String commonPleas) {
        this.commonPleas = commonPleas;
    }

    public String getJgName() {
        return jgName;
    }

    public void setJgName(String jgName) {
        this.jgName = jgName;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getLendingBank() {
        return lendingBank;
    }

    public void setLendingBank(String lendingBank) {
        this.lendingBank = lendingBank;
    }

    public String getLitigationStatus() {
        return litigationStatus;
    }

    public void setLitigationStatus(String litigationStatus) {
        this.litigationStatus = litigationStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("litigationCaseId", getLitigationCaseId())
            .append("lawyerFee", getLawyerFee())
            .append("litigationFee", getLitigationFee())
            .append("preservationFee", getPreservationFee())
            .append("surveillanceFee", getSurveillanceFee())
            .append("announcementFee", getAnnouncementFee())
            .append("appraisalFee", getAppraisalFee())
            .append("executionFee", getExecutionFee())
            .append("penalty", getPenalty())
            .append("guaranteeFee", getGuaranteeFee())
            .append("intermediaryFee", getIntermediaryFee())
            .append("compensity", getCompensity())
            .append("judgmentAmount", getJudgmentAmount())
            .append("specialChannelFees", getSpecialChannelFees())
            .append("interest", getInterest())
            .append("otherAmountsOwed", getOtherAmountsOwed())
            .append("insurance", getInsurance())
            .append("approvalStatus", getApprovalStatus())
            .append("reasons", getReasons())
            .append("approveBy", getApproveBy())
            .append("approveRole", getApproveRole())
            .append("applicationTime", getApplicationTime())
            .append("applicationBy", getApplicationBy())
            .append("totalMoney", getTotalMoney())
            .append("submissionCount", getSubmissionCount())
            .append("costCategory", getCostCategory())
            .append("approveTime", getApproveTime())
            .toString();
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getOverallApprovalStatus() {
        return overallApprovalStatus;
    }

    public void setOverallApprovalStatus(String overallApprovalStatus) {
        this.overallApprovalStatus = overallApprovalStatus;
    }

    public String getApprovalStartTime() {
        return approvalStartTime;
    }

    public void setApprovalStartTime(String approvalStartTime) {
        this.approvalStartTime = approvalStartTime;
    }

    public String getApprovalEndTime() {
        return approvalEndTime;
    }

    public void setApprovalEndTime(String approvalEndTime) {
        this.approvalEndTime = approvalEndTime;
    }
}
