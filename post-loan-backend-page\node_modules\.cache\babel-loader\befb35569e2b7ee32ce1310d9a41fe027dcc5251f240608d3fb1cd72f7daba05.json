{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _objectSpread2 = _interopRequireDefault(require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/objectSpread2.js\"));\nvar _typeof2 = _interopRequireDefault(require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/typeof.js\"));\nrequire(\"core-js/modules/es.array.concat.js\");\nrequire(\"core-js/modules/es.array.map.js\");\nrequire(\"core-js/modules/es.object.to-string.js\");\nrequire(\"core-js/modules/esnext.iterator.constructor.js\");\nrequire(\"core-js/modules/esnext.iterator.map.js\");\nvar _litigation_cost_approval = require(\"@/api/litigation_cost_approval/litigation_cost_approval\");\nvar _area = _interopRequireDefault(require(\"../../../assets/area.json\"));\nvar _userInfo = _interopRequireDefault(require(\"@/layout/components/Dialog/userInfo.vue\"));\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: \"Vm_car_order\",\n  components: {\n    userInfo: _userInfo.default\n  },\n  data: function data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // VIEW表格数据\n      vm_car_orderList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        // 严格按照9个筛选条件\n        // 1. 贷款人姓名\n        customerName: null,\n        // 2. 贷款人身份证号\n        certId: null,\n        // 3. 出单渠道\n        jgName: null,\n        // 4. 放款银行\n        lendingBank: null,\n        // 5. 法诉状态(多级)\n        litigationStatus: null,\n        // 6. 申请人\n        applicationBy: null,\n        // 7. 费用类型\n        costCategory: null,\n        // 8. 审批状态\n        approvalStatus: null,\n        // 9. 申请时间区间\n        startTime: null,\n        endTime: null,\n        // 10. 审批时间区间\n        approvalStartTime: null,\n        approvalEndTime: null\n      },\n      // 日期范围\n      dateRange: [],\n      // 审批日期范围\n      approvalDateRange: [],\n      // 表单参数\n      form: {\n        id: '',\n        status: 0,\n        rejectReason: null\n      },\n      // 当前审批记录\n      currentRecord: {},\n      // 费用提交记录列表\n      submissionRecords: [],\n      // 记录加载状态\n      recordsLoading: false,\n      // 选中的记录\n      selectedRecords: [],\n      // 单个审批对话框\n      singleApprovalOpen: false,\n      singleApprovalForm: {\n        id: '',\n        action: '',\n        // 'approve' 或 'reject'\n        rejectReason: ''\n      },\n      // 批量审批对话框\n      batchApprovalOpen: false,\n      batchApprovalForm: {\n        action: '',\n        // 'approve' 或 'reject'\n        rejectReason: ''\n      },\n      // 贷款人信息相关\n      userInfoVisible: false,\n      customerInfo: {},\n      // 法诉状态多级选项\n      litigationStatusOptions: [{\n        value: '起诉',\n        label: '起诉',\n        children: [{\n          value: '起诉-准备材料',\n          label: '准备材料'\n        }, {\n          value: '起诉-已提交',\n          label: '已提交'\n        }, {\n          value: '起诉-法院受理',\n          label: '法院受理'\n        }]\n      }, {\n        value: '审理',\n        label: '审理',\n        children: [{\n          value: '审理-开庭审理',\n          label: '开庭审理'\n        }, {\n          value: '审理-等待判决',\n          label: '等待判决'\n        }, {\n          value: '审理-一审判决',\n          label: '一审判决'\n        }]\n      }, {\n        value: '执行',\n        label: '执行',\n        children: [{\n          value: '执行-申请执行',\n          label: '申请执行'\n        }, {\n          value: '执行-执行中',\n          label: '执行中'\n        }, {\n          value: '执行-执行完毕',\n          label: '执行完毕'\n        }]\n      }, {\n        value: '结案',\n        label: '结案',\n        children: [{\n          value: '结案-胜诉结案',\n          label: '胜诉结案'\n        }, {\n          value: '结案-败诉结案',\n          label: '败诉结案'\n        }, {\n          value: '结案-和解结案',\n          label: '和解结案'\n        }]\n      }],\n      // 表单校验\n      rules: {\n        keyProvince: '',\n        keyCity: '',\n        keyBorough: '',\n        keyDetailAddress: ''\n      },\n      provinceList: _area.default,\n      cityList: [],\n      districtList: []\n    };\n  },\n  created: function created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询法诉费用审批列表 */getList: function getList() {\n      var _this = this;\n      this.loading = true;\n      (0, _litigation_cost_approval.listLitigationCostApproval)(this.queryParams).then(function (response) {\n        _this.vm_car_orderList = response.rows;\n        _this.total = response.total;\n        _this.loading = false;\n      });\n    },\n    // 取消按钮\n    cancel: function cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset: function reset() {\n      this.form = {\n        id: '',\n        status: 0,\n        rejectReason: null\n      };\n      this.currentRecord = {};\n      this.submissionRecords = [];\n      this.selectedRecords = [];\n      this.singleApprovalOpen = false;\n      this.batchApprovalOpen = false;\n      this.singleApprovalForm = {\n        id: '',\n        action: '',\n        rejectReason: ''\n      };\n      this.batchApprovalForm = {\n        action: '',\n        rejectReason: ''\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */handleQuery: function handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */resetQuery: function resetQuery() {\n      this.dateRange = [];\n      this.approvalDateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange: function handleSelectionChange(selection) {\n      this.ids = selection.map(function (item) {\n        return item.id;\n      });\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    /** 新增按钮操作 */handleAdd: function handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加VIEW\";\n    },\n    /** 修改按钮操作 */handleUpdate: function handleUpdate(row) {\n      this.currentRecord = row;\n      this.loadSubmissionRecords(row.litigationCaseId);\n      this.open = true;\n    },\n    /** 批量修改按钮操作 */handleBatchEdit: function handleBatchEdit() {\n      this.$modal.msgError('请选择单条记录进行审批操作');\n    },\n    /** 加载费用提交记录 */loadSubmissionRecords: function loadSubmissionRecords(litigationCaseId) {\n      var _this2 = this;\n      this.recordsLoading = true;\n      (0, _litigation_cost_approval.getLitigationCostSubmissionRecords)(litigationCaseId).then(function (response) {\n        _this2.submissionRecords = response.data || [];\n        _this2.recordsLoading = false;\n      }).catch(function () {\n        _this2.recordsLoading = false;\n      });\n    },\n    /** 记录选择变化 */handleRecordSelectionChange: function handleRecordSelectionChange(selection) {\n      this.selectedRecords = selection;\n    },\n    /** 单个审批 */handleSingleApprove: function handleSingleApprove(record, action) {\n      console.log('=== 前端单个审批点击 ===');\n      console.log('选中的记录:', record);\n      console.log('记录ID:', record.id, '类型:', (0, _typeof2.default)(record.id));\n      console.log('操作类型:', action);\n      this.singleApprovalForm.id = record.id;\n      this.singleApprovalForm.action = action;\n      this.singleApprovalForm.rejectReason = '';\n      this.singleApprovalOpen = true;\n      console.log('设置后的singleApprovalForm:', this.singleApprovalForm);\n    },\n    /** 确认单个审批 */confirmSingleApproval: function confirmSingleApproval() {\n      var _this3 = this;\n      if (this.singleApprovalForm.action === 'reject') {\n        this.$refs[\"singleApprovalForm\"].validate(function (valid) {\n          if (!valid) return;\n          _this3.executeSingleApproval();\n        });\n      } else {\n        this.executeSingleApproval();\n      }\n    },\n    /** 执行单个审批 */executeSingleApproval: function executeSingleApproval() {\n      var _this4 = this;\n      console.log('=== 前端执行单个审批开始 ===');\n      console.log('singleApprovalForm:', this.singleApprovalForm);\n      var data = {\n        id: this.singleApprovalForm.id,\n        action: this.singleApprovalForm.action,\n        rejectReason: this.singleApprovalForm.rejectReason\n      };\n      console.log('发送到后端的数据:', data);\n      console.log('ID类型:', (0, _typeof2.default)(data.id), 'ID值:', data.id);\n      (0, _litigation_cost_approval.singleApproveLitigationCostNew)(data).then(function () {\n        console.log('审批成功');\n        _this4.$modal.msgSuccess(\"\".concat(_this4.singleApprovalForm.action === 'approve' ? '通过' : '拒绝', \"\\u5BA1\\u6279\\u6210\\u529F\"));\n        _this4.singleApprovalOpen = false;\n        _this4.loadSubmissionRecords(_this4.currentRecord.litigationCaseId);\n        _this4.getList();\n      }).catch(function (error) {\n        console.error('审批失败:', error);\n      });\n    },\n    /** 批量审批 */handleBatchApprove: function handleBatchApprove(action) {\n      if (this.selectedRecords.length === 0) {\n        this.$modal.msgError('请选择要审批的记录');\n        return;\n      }\n      this.batchApprovalForm.action = action;\n      this.batchApprovalForm.rejectReason = '';\n      this.batchApprovalOpen = true;\n    },\n    /** 确认批量审批 */confirmBatchApproval: function confirmBatchApproval() {\n      var _this5 = this;\n      if (this.batchApprovalForm.action === 'reject') {\n        this.$refs[\"batchApprovalForm\"].validate(function (valid) {\n          if (!valid) return;\n          _this5.executeBatchApproval();\n        });\n      } else {\n        this.executeBatchApproval();\n      }\n    },\n    /** 执行批量审批 */executeBatchApproval: function executeBatchApproval() {\n      var _this6 = this;\n      var data = {\n        ids: this.selectedRecords.map(function (record) {\n          return record.id;\n        }),\n        action: this.batchApprovalForm.action,\n        rejectReason: this.batchApprovalForm.rejectReason\n      };\n      (0, _litigation_cost_approval.batchApproveLitigationCostNew)(data).then(function () {\n        _this6.$modal.msgSuccess(\"\\u6279\\u91CF\".concat(_this6.batchApprovalForm.action === 'approve' ? '通过' : '拒绝', \"\\u5BA1\\u6279\\u6210\\u529F\"));\n        _this6.batchApprovalOpen = false;\n        _this6.selectedRecords = [];\n        _this6.loadSubmissionRecords(_this6.currentRecord.litigationCaseId);\n        _this6.getList();\n      }).catch(function (error) {\n        console.error('批量审批失败:', error);\n      });\n    },\n    /** 主列表批量审批 */handleBatchApproveMain: function handleBatchApproveMain(status) {\n      var _this7 = this;\n      if (this.ids.length === 0) {\n        this.$modal.msgError('请选择要审批的记录');\n        return;\n      }\n      var statusText = status === '0' ? '通过' : '拒绝';\n      this.$modal.confirm(\"\\u786E\\u8BA4\\u8981\\u6279\\u91CF\".concat(statusText, \"\\u9009\\u4E2D\\u7684 \").concat(this.ids.length, \" \\u6761\\u8BB0\\u5F55\\u5417\\uFF1F\")).then(function () {\n        var data = {\n          ids: _this7.ids,\n          status: status,\n          rejectReason: status === '1' ? '批量拒绝' : ''\n        };\n        return (0, _litigation_cost_approval.batchApproveLitigationCostRecords)(data);\n      }).then(function () {\n        _this7.$modal.msgSuccess(\"\\u6279\\u91CF\".concat(statusText, \"\\u6210\\u529F\"));\n        _this7.getList();\n      }).catch(function () {});\n    },\n    /** 删除按钮操作 */handleDelete: function handleDelete() {\n      var _this8 = this;\n      this.$modal.confirm('是否确认删除选中的数据项？').then(function () {\n        // 这里可以调用删除API，暂时只是提示\n        _this8.$modal.msgSuccess(\"删除功能暂未实现\");\n      }).catch(function () {});\n    },\n    /** 导出按钮操作 */handleExport: function handleExport() {\n      this.download('litigation_cost_approval/litigation_cost_approval/export', (0, _objectSpread2.default)({}, this.queryParams), \"litigation_cost_approval_\".concat(new Date().getTime(), \".xlsx\"));\n    },\n    /** 打开贷款人信息 */openUserInfo: function openUserInfo(row) {\n      if (!row.customerId && !row.applyId) {\n        this.$modal.msgError('无法获取贷款人信息');\n        return;\n      }\n      this.customerInfo = {\n        customerId: row.customerId,\n        applyId: row.applyId,\n        customerName: row.customerName\n      };\n      this.userInfoVisible = true;\n    },\n    /** 处理日期范围变化 */handleDateRangeChange: function handleDateRangeChange(dates) {\n      if (dates && dates.length === 2) {\n        this.queryParams.startTime = dates[0];\n        this.queryParams.endTime = dates[1];\n      } else {\n        this.queryParams.startTime = null;\n        this.queryParams.endTime = null;\n      }\n      this.handleQuery();\n    },\n    /** 处理审批时间范围变化 */handleApprovalDateRangeChange: function handleApprovalDateRangeChange(dates) {\n      if (dates && dates.length === 2) {\n        this.queryParams.approvalStartTime = dates[0];\n        this.queryParams.approvalEndTime = dates[1];\n      } else {\n        this.queryParams.approvalStartTime = null;\n        this.queryParams.approvalEndTime = null;\n      }\n      this.handleQuery();\n    },\n    /** 获取状态文本 */getStatusText: function getStatusText(status) {\n      var statusMap = {\n        '0': '未审批',\n        '1': '全部同意',\n        '2': '已拒绝',\n        '3': '法诉主管审批',\n        '4': '总监审批',\n        '5': '财务主管/总监抄送',\n        '6': '总经理/董事长审批'\n      };\n      return statusMap[status] || '未知状态';\n    },\n    /** 检查记录是否可以审批 */canApproveRecord: function canApproveRecord(record) {\n      // 根据当前用户角色和记录状态判断是否可以审批\n      // 这里简化处理，实际应该根据用户角色和审批状态来判断\n      return record.approvalStatus === '0' || record.approvalStatus === null || record.approvalStatus === '' || record.approvalStatus === '3' || record.approvalStatus === '4' || record.approvalStatus === '5' || record.approvalStatus === '6';\n    }\n  }\n};", "map": {"version": 3, "names": ["_litigation_cost_approval", "require", "_area", "_interopRequireDefault", "_userInfo", "name", "components", "userInfo", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "vm_car_orderList", "title", "open", "queryParams", "pageNum", "pageSize", "customerName", "certId", "jgName", "lendingBank", "litigationStatus", "applicationBy", "costCategory", "approvalStatus", "startTime", "endTime", "approvalStartTime", "approvalEndTime", "date<PERSON><PERSON><PERSON>", "approvalDateRange", "form", "id", "status", "rejectReason", "currentRecord", "submissionRecords", "recordsLoading", "selected<PERSON><PERSON><PERSON><PERSON>", "singleApprovalOpen", "singleApprovalForm", "action", "batchApprovalOpen", "batchApprovalForm", "userInfoVisible", "customerInfo", "litigationStatusOptions", "value", "label", "children", "rules", "key<PERSON><PERSON>ince", "keyCity", "<PERSON><PERSON><PERSON><PERSON>", "keyDetailAddress", "provinceList", "areaList", "cityList", "districtList", "created", "getList", "methods", "_this", "listLitigationCostApproval", "then", "response", "rows", "cancel", "reset", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "loadSubmissionRecords", "litigationCaseId", "handleBatchEdit", "$modal", "msgError", "_this2", "getLitigationCostSubmissionRecords", "catch", "handleRecordSelectionChange", "handleSingleApprove", "record", "console", "log", "_typeof2", "default", "confirmSingleApproval", "_this3", "$refs", "validate", "valid", "executeSingleApproval", "_this4", "singleApproveLitigationCostNew", "msgSuccess", "concat", "error", "handleBatchApprove", "confirmBatchApproval", "_this5", "executeBatchApproval", "_this6", "batchApproveLitigationCostNew", "handleBatchApproveMain", "_this7", "statusText", "confirm", "batchApproveLitigationCostRecords", "handleDelete", "_this8", "handleExport", "download", "_objectSpread2", "Date", "getTime", "openUserInfo", "customerId", "applyId", "handleDateRangeChange", "dates", "handleApprovalDateRangeChange", "getStatusText", "statusMap", "canApproveRecord"], "sources": ["src/views/litigation/litigation/litigation_approval.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <!-- 1. 贷款人姓名 -->\r\n      <el-form-item label=\"\" prop=\"customerName\">\r\n        <el-input\r\n          v-model=\"queryParams.customerName\"\r\n          placeholder=\"贷款人姓名\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 2. 贷款人身份证号 -->\r\n      <el-form-item label=\"\" prop=\"certId\">\r\n        <el-input\r\n          v-model=\"queryParams.certId\"\r\n          placeholder=\"贷款人身份证号\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 3. 出单渠道 -->\r\n      <el-form-item label=\"\" prop=\"jgName\">\r\n        <el-input\r\n          v-model=\"queryParams.jgName\"\r\n          placeholder=\"出单渠道\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 4. 放款银行 -->\r\n      <el-form-item label=\"\" prop=\"lendingBank\">\r\n        <el-input\r\n          v-model=\"queryParams.lendingBank\"\r\n          placeholder=\"放款银行\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 5. 法诉状态(需呈现多级) -->\r\n      <el-form-item label=\"\" prop=\"litigationStatus\">\r\n        <el-cascader\r\n          v-model=\"queryParams.litigationStatus\"\r\n          :options=\"litigationStatusOptions\"\r\n          :props=\"{ expandTrigger: 'hover', value: 'value', label: 'label', children: 'children' }\"\r\n          placeholder=\"法诉状态\"\r\n          clearable\r\n          @change=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 6. 申请人 -->\r\n      <el-form-item label=\"\" prop=\"applicationBy\">\r\n        <el-input\r\n          v-model=\"queryParams.applicationBy\"\r\n          placeholder=\"申请人\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 7. 费用类型 -->\r\n      <el-form-item label=\"\" prop=\"costCategory\">\r\n        <el-select v-model=\"queryParams.costCategory\" placeholder=\"费用类型\" clearable @change=\"handleQuery\">\r\n          <el-option label=\"律师费\" value=\"律师费\" />\r\n          <el-option label=\"诉讼费\" value=\"诉讼费\" />\r\n          <el-option label=\"保全费\" value=\"保全费\" />\r\n          <el-option label=\"执行费\" value=\"执行费\" />\r\n          <el-option label=\"其他费用\" value=\"其他费用\" />\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <!-- 8. 审批状态 -->\r\n      <el-form-item label=\"\" prop=\"approvalStatus\">\r\n        <el-select v-model=\"queryParams.approvalStatus\" placeholder=\"审批状态\" clearable @change=\"handleQuery\">\r\n          <el-option label=\"未审批\" value=\"0\" />\r\n          <el-option label=\"全部同意\" value=\"1\" />\r\n          <el-option label=\"已拒绝\" value=\"2\" />\r\n          <el-option label=\"法诉主管审批\" value=\"3\" />\r\n          <el-option label=\"总监审批\" value=\"4\" />\r\n          <el-option label=\"财务主管/总监抄送\" value=\"5\" />\r\n          <el-option label=\"总经理/董事长审批\" value=\"6\" />\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <!-- 9. 申请时间区间 -->\r\n      <el-form-item label=\"\" prop=\"dateRange\">\r\n        <el-date-picker\r\n          v-model=\"dateRange\"\r\n          type=\"daterange\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"申请开始日期\"\r\n          end-placeholder=\"申请结束日期\"\r\n          format=\"yyyy-MM-dd\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          @change=\"handleDateRangeChange\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 10. 审批时间区间 -->\r\n      <el-form-item label=\"\" prop=\"approvalDateRange\">\r\n        <el-date-picker\r\n          v-model=\"approvalDateRange\"\r\n          type=\"daterange\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"审批开始日期\"\r\n          end-placeholder=\"审批结束日期\"\r\n          format=\"yyyy-MM-dd\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          @change=\"handleApprovalDateRangeChange\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['vm_car_order:vm_car_order:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleBatchEdit\"\r\n          v-hasPermi=\"['vm_car_order:vm_car_order:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['vm_car_order:vm_car_order:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['vm_car_order:vm_car_order:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"vm_car_orderList\" @selection-change=\"handleSelectionChange\" row-key=\"id\" style=\"width: 100%\" flex=\"right\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"序号\" align=\"center\" type=\"index\" width=\"60\" fixed />\r\n      <el-table-column label=\"申请人\" align=\"center\" prop=\"applicationBy\" width=\"100\" />\r\n      <el-table-column label=\"最新申请时间\" align=\"center\" prop=\"applicationTime\" width=\"150\" />\r\n      <el-table-column label=\"案件负责人\" align=\"center\" prop=\"curator\" width=\"100\" />\r\n      <el-table-column label=\"提交次数\" align=\"center\" prop=\"submissionCount\" width=\"100\" />\r\n      <el-table-column label=\"法诉状态\" align=\"center\" prop=\"litigationStatus\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{scope.row.litigationStatus == '1'?'待立案':'已邮寄'}}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"贷款人\" align=\"center\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            type=\"text\"\r\n            @click=\"openUserInfo(scope.row)\"\r\n            style=\"color: #409EFF;\">\r\n            {{ scope.row.customerName }}\r\n          </el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"出单渠道\" align=\"center\" prop=\"jgName\" />\r\n      <el-table-column label=\"地区\" align=\"center\" prop=\"area\" width=\"80\" />\r\n      <el-table-column label=\"放款银行\" align=\"center\" prop=\"lendingBank\" />\r\n      <el-table-column label=\"法院地\" align=\"center\" prop=\"courtLocation\" />\r\n      <el-table-column label=\"诉讼法院\" align=\"center\" prop=\"commonPleas\" />\r\n      <el-table-column label=\"律师费\" align=\"center\" prop=\"lawyerFee\" width=\"80\" />\r\n      <el-table-column label=\"诉讼费\" align=\"center\" prop=\"litigationFee\" width=\"80\" />\r\n      <el-table-column label=\"保险费\" align=\"center\" prop=\"insurance\" width=\"80\" />\r\n      <el-table-column label=\"保全费\" align=\"center\" prop=\"preservationFee\" width=\"80\" />\r\n      <el-table-column label=\"布控费\" align=\"center\" prop=\"surveillanceFee\" width=\"80\" />\r\n      <el-table-column label=\"公告费\" align=\"center\" prop=\"announcementFee\" width=\"80\" />\r\n      <el-table-column label=\"评估费\" align=\"center\" prop=\"appraisalFee\" width=\"80\" />\r\n      <el-table-column label=\"执行费\" align=\"center\" prop=\"executionFee\" width=\"80\" />\r\n      <el-table-column label=\"特殊通道费\" align=\"center\" prop=\"specialChannelFees\" width=\"100\" />\r\n      <el-table-column label=\"日常报销\" align=\"center\" prop=\"otherAmountsOwed\" width=\"80\" />\r\n      <el-table-column label=\"总费用\" align=\"center\" prop=\"totalMoney\" width=\"100\" />\r\n      <el-table-column label=\"审批状态\" align=\"center\" prop=\"approvalStatus\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.approvalStatus == '0' || scope.row.approvalStatus == null || scope.row.approvalStatus == ''\" type=\"info\">未审批</el-tag>\r\n          <el-tag v-else-if=\"scope.row.approvalStatus == '1'\" type=\"success\">全部同意</el-tag>\r\n          <el-tag v-else-if=\"scope.row.approvalStatus == '2'\" type=\"danger\">已拒绝</el-tag>\r\n          <el-tag v-else-if=\"scope.row.approvalStatus == '3'\" type=\"warning\">法诉主管审批</el-tag>\r\n          <el-tag v-else-if=\"scope.row.approvalStatus == '4'\" type=\"warning\">总监审批</el-tag>\r\n          <el-tag v-else-if=\"scope.row.approvalStatus == '5'\" type=\"warning\">财务主管/总监抄送</el-tag>\r\n          <el-tag v-else-if=\"scope.row.approvalStatus == '6'\" type=\"warning\">总经理/董事长审批</el-tag>\r\n          <el-tag v-else type=\"info\">{{ getStatusText(scope.row.approvalStatus) }}</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"审批时间\" align=\"center\" prop=\"approveTime\" width=\"150\" />\r\n      <el-table-column label=\"审批人角色\" align=\"center\" prop=\"approveRole\" />\r\n      <el-table-column label=\"审批人\" align=\"center\" prop=\"approveBy\" />\r\n      <el-table-column label=\"操作\" align=\"center\" width=\"100\" class-name=\"small-padding fixed-width\" fixed=\"right\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['vm_car_order:vm_car_order:edit']\"\r\n          >审批</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 审批对话框 -->\r\n    <el-dialog title=\"法诉费用审批详情\" :visible.sync=\"open\" width=\"1200px\" append-to-body>\r\n      <div class=\"approval-header\">\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <strong>贷款人：</strong>{{ currentRecord.customerName }}\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <strong>案件负责人：</strong>{{ currentRecord.curator }}\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <strong>法院地：</strong>{{ currentRecord.courtLocation }}\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <div class=\"batch-approval-section\" style=\"margin: 20px 0;\">\r\n        <el-button\r\n          type=\"success\"\r\n          size=\"small\"\r\n          :disabled=\"selectedRecords.length === 0\"\r\n          @click=\"handleBatchApprove('approve')\">\r\n          批量通过 ({{ selectedRecords.length }})\r\n        </el-button>\r\n        <el-button\r\n          type=\"danger\"\r\n          size=\"small\"\r\n          :disabled=\"selectedRecords.length === 0\"\r\n          @click=\"handleBatchApprove('reject')\">\r\n          批量拒绝 ({{ selectedRecords.length }})\r\n        </el-button>\r\n      </div>\r\n\r\n      <el-table\r\n        :data=\"submissionRecords\"\r\n        @selection-change=\"handleRecordSelectionChange\"\r\n        v-loading=\"recordsLoading\">\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" fixed=\"left\" />\r\n        <el-table-column label=\"提交时间\" align=\"center\" prop=\"applicationTime\" width=\"150\" />\r\n        <el-table-column label=\"提交人\" align=\"center\" prop=\"applicationBy\" width=\"100\" />\r\n        <el-table-column label=\"律师费\" align=\"center\" prop=\"lawyerFee\" width=\"80\" />\r\n        <el-table-column label=\"诉讼费\" align=\"center\" prop=\"litigationFee\" width=\"80\" />\r\n        <el-table-column label=\"保全费\" align=\"center\" prop=\"preservationFee\" width=\"80\" />\r\n        <el-table-column label=\"布控费\" align=\"center\" prop=\"surveillanceFee\" width=\"80\" />\r\n        <el-table-column label=\"公告费\" align=\"center\" prop=\"announcementFee\" width=\"80\" />\r\n        <el-table-column label=\"评估费\" align=\"center\" prop=\"appraisalFee\" width=\"80\" />\r\n        <el-table-column label=\"执行费\" align=\"center\" prop=\"executionFee\" width=\"80\" />\r\n        <el-table-column label=\"违约金\" align=\"center\" prop=\"penalty\" width=\"80\" />\r\n        <el-table-column label=\"担保费\" align=\"center\" prop=\"guaranteeFee\" width=\"80\" />\r\n        <el-table-column label=\"居间费\" align=\"center\" prop=\"intermediaryFee\" width=\"80\" />\r\n        <el-table-column label=\"代偿金\" align=\"center\" prop=\"compensity\" width=\"80\" />\r\n        <el-table-column label=\"判决金额\" align=\"center\" prop=\"judgmentAmount\" width=\"100\" />\r\n        <el-table-column label=\"利息\" align=\"center\" prop=\"interest\" width=\"80\" />\r\n        <el-table-column label=\"其他欠款\" align=\"center\" prop=\"otherAmountsOwed\" width=\"100\" />\r\n        <el-table-column label=\"保险费\" align=\"center\" prop=\"insurance\" width=\"80\" />\r\n        <el-table-column label=\"总费用\" align=\"center\" prop=\"totalMoney\" width=\"100\" />\r\n        <el-table-column label=\"审批状态\" align=\"center\" prop=\"approvalStatus\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag v-if=\"scope.row.approvalStatus == '0' || scope.row.approvalStatus == null || scope.row.approvalStatus == ''\" type=\"info\">未审批</el-tag>\r\n            <el-tag v-else-if=\"scope.row.approvalStatus == '1'\" type=\"success\">全部同意</el-tag>\r\n            <el-tag v-else-if=\"scope.row.approvalStatus == '2'\" type=\"danger\">已拒绝</el-tag>\r\n            <el-tag v-else-if=\"scope.row.approvalStatus == '3'\" type=\"warning\">法诉主管审批</el-tag>\r\n            <el-tag v-else-if=\"scope.row.approvalStatus == '4'\" type=\"warning\">总监审批</el-tag>\r\n            <el-tag v-else-if=\"scope.row.approvalStatus == '5'\" type=\"warning\">财务主管/总监抄送</el-tag>\r\n            <el-tag v-else-if=\"scope.row.approvalStatus == '6'\" type=\"warning\">总经理/董事长审批</el-tag>\r\n            <el-tag v-else type=\"warning\">{{ getStatusText(scope.row.approvalStatus) }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"审批时间\" align=\"center\" prop=\"approveTime\" width=\"150\" />\r\n        <el-table-column label=\"审批人\" align=\"center\" prop=\"approveBy\" width=\"100\" />\r\n        <el-table-column label=\"拒绝原因\" align=\"center\" prop=\"reasons\" width=\"150\" />\r\n        <el-table-column label=\"操作\" align=\"center\" width=\"150\" fixed=\"right\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              v-if=\"canApproveRecord(scope.row)\"\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"handleSingleApprove(scope.row, 'approve')\">\r\n              通过\r\n            </el-button>\r\n            <el-button\r\n              v-if=\"canApproveRecord(scope.row)\"\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"handleSingleApprove(scope.row, 'reject')\">\r\n              拒绝\r\n            </el-button>\r\n            <div v-else>\r\n              <el-tag v-if=\"scope.row.approvalStatus == '1'\" type=\"success\" size=\"mini\">全部同意</el-tag>\r\n              <el-tag v-else-if=\"scope.row.approvalStatus == '2'\" type=\"danger\" size=\"mini\">已拒绝</el-tag>\r\n              <el-tag v-else-if=\"scope.row.approvalStatus == '3'\" type=\"warning\" size=\"mini\">法诉主管审批</el-tag>\r\n              <el-tag v-else-if=\"scope.row.approvalStatus == '4'\" type=\"warning\" size=\"mini\">总监审批</el-tag>\r\n              <el-tag v-else-if=\"scope.row.approvalStatus == '5'\" type=\"warning\" size=\"mini\">财务主管/总监抄送</el-tag>\r\n              <el-tag v-else-if=\"scope.row.approvalStatus == '6'\" type=\"warning\" size=\"mini\">总经理/董事长审批</el-tag>\r\n              <div v-if=\"scope.row.approveBy\" style=\"font-size: 12px; color: #999; margin-top: 2px;\">\r\n                {{ scope.row.approveBy }}\r\n              </div>\r\n              <div v-if=\"scope.row.approveTime\" style=\"font-size: 12px; color: #999;\">\r\n                {{ scope.row.approveTime }}\r\n              </div>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancel\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 单个审批确认对话框 -->\r\n    <el-dialog title=\"审批确认\" :visible.sync=\"singleApprovalOpen\" width=\"400px\" append-to-body>\r\n      <el-form ref=\"singleApprovalForm\" :model=\"singleApprovalForm\" label-width=\"80px\">\r\n        <el-form-item label=\"审批结果\">\r\n          <el-tag :type=\"singleApprovalForm.action === 'approve' ? 'success' : 'danger'\">\r\n            {{ singleApprovalForm.action === 'approve' ? '通过' : '拒绝' }}\r\n          </el-tag>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"拒绝原因\"\r\n          prop=\"rejectReason\"\r\n          v-if=\"singleApprovalForm.action === 'reject'\"\r\n          :rules=\"[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]\">\r\n          <el-input\r\n            v-model=\"singleApprovalForm.rejectReason\"\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入拒绝原因\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"confirmSingleApproval\">确 定</el-button>\r\n        <el-button @click=\"singleApprovalOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批量审批确认对话框 -->\r\n    <el-dialog title=\"批量审批确认\" :visible.sync=\"batchApprovalOpen\" width=\"400px\" append-to-body>\r\n      <el-form ref=\"batchApprovalForm\" :model=\"batchApprovalForm\" label-width=\"80px\">\r\n        <el-form-item label=\"审批结果\">\r\n          <el-tag :type=\"batchApprovalForm.action === 'approve' ? 'success' : 'danger'\">\r\n            {{ batchApprovalForm.action === 'approve' ? '批量通过' : '批量拒绝' }}\r\n          </el-tag>\r\n        </el-form-item>\r\n        <el-form-item label=\"选中记录\">\r\n          <span>{{ selectedRecords.length }} 条记录</span>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"拒绝原因\"\r\n          prop=\"rejectReason\"\r\n          v-if=\"batchApprovalForm.action === 'reject'\"\r\n          :rules=\"[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]\">\r\n          <el-input\r\n            v-model=\"batchApprovalForm.rejectReason\"\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入拒绝原因\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"confirmBatchApproval\">确 定</el-button>\r\n        <el-button @click=\"batchApprovalOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 贷款人信息对话框 -->\r\n    <userInfo ref=\"userInfo\" :visible.sync=\"userInfoVisible\" title=\"贷款人信息\" :customerInfo=\"customerInfo\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listLitigationCostApproval,\r\n  getLitigationCostSubmissionRecords,\r\n  approveLitigationCostRecord,\r\n  batchApproveLitigationCostRecords,\r\n  singleApproveLitigationCostNew,\r\n  batchApproveLitigationCostNew\r\n} from \"@/api/litigation_cost_approval/litigation_cost_approval\"\r\nimport areaList from \"../../../assets/area.json\"\r\nimport userInfo from '@/layout/components/Dialog/userInfo.vue'\r\nexport default {\r\n  name: \"Vm_car_order\",\r\n  components: {\r\n    userInfo,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // VIEW表格数据\r\n      vm_car_orderList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        // 严格按照9个筛选条件\r\n        // 1. 贷款人姓名\r\n        customerName: null,\r\n        // 2. 贷款人身份证号\r\n        certId: null,\r\n        // 3. 出单渠道\r\n        jgName: null,\r\n        // 4. 放款银行\r\n        lendingBank: null,\r\n        // 5. 法诉状态(多级)\r\n        litigationStatus: null,\r\n        // 6. 申请人\r\n        applicationBy: null,\r\n        // 7. 费用类型\r\n        costCategory: null,\r\n        // 8. 审批状态\r\n        approvalStatus: null,\r\n        // 9. 申请时间区间\r\n        startTime: null,\r\n        endTime: null,\r\n        // 10. 审批时间区间\r\n        approvalStartTime: null,\r\n        approvalEndTime: null,\r\n      },\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 审批日期范围\r\n      approvalDateRange: [],\r\n      // 表单参数\r\n      form: {\r\n        id:'',\r\n        status: 0,\r\n        rejectReason:null,\r\n      },\r\n      // 当前审批记录\r\n      currentRecord: {},\r\n      // 费用提交记录列表\r\n      submissionRecords: [],\r\n      // 记录加载状态\r\n      recordsLoading: false,\r\n      // 选中的记录\r\n      selectedRecords: [],\r\n      // 单个审批对话框\r\n      singleApprovalOpen: false,\r\n      singleApprovalForm: {\r\n        id: '',\r\n        action: '', // 'approve' 或 'reject'\r\n        rejectReason: ''\r\n      },\r\n      // 批量审批对话框\r\n      batchApprovalOpen: false,\r\n      batchApprovalForm: {\r\n        action: '', // 'approve' 或 'reject'\r\n        rejectReason: ''\r\n      },\r\n      // 贷款人信息相关\r\n      userInfoVisible: false,\r\n      customerInfo: {},\r\n      // 法诉状态多级选项\r\n      litigationStatusOptions: [\r\n        {\r\n          value: '起诉',\r\n          label: '起诉',\r\n          children: [\r\n            { value: '起诉-准备材料', label: '准备材料' },\r\n            { value: '起诉-已提交', label: '已提交' },\r\n            { value: '起诉-法院受理', label: '法院受理' }\r\n          ]\r\n        },\r\n        {\r\n          value: '审理',\r\n          label: '审理',\r\n          children: [\r\n            { value: '审理-开庭审理', label: '开庭审理' },\r\n            { value: '审理-等待判决', label: '等待判决' },\r\n            { value: '审理-一审判决', label: '一审判决' }\r\n          ]\r\n        },\r\n        {\r\n          value: '执行',\r\n          label: '执行',\r\n          children: [\r\n            { value: '执行-申请执行', label: '申请执行' },\r\n            { value: '执行-执行中', label: '执行中' },\r\n            { value: '执行-执行完毕', label: '执行完毕' }\r\n          ]\r\n        },\r\n        {\r\n          value: '结案',\r\n          label: '结案',\r\n          children: [\r\n            { value: '结案-胜诉结案', label: '胜诉结案' },\r\n            { value: '结案-败诉结案', label: '败诉结案' },\r\n            { value: '结案-和解结案', label: '和解结案' }\r\n          ]\r\n        }\r\n      ],\r\n      // 表单校验\r\n      rules: {\r\n        keyProvince:'',\r\n        keyCity:'',\r\n        keyBorough:'',\r\n        keyDetailAddress:'',\r\n      },\r\n      provinceList:areaList,\r\n      cityList:[],\r\n      districtList:[]\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    /** 查询法诉费用审批列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listLitigationCostApproval(this.queryParams).then(response => {\r\n        this.vm_car_orderList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id:'',\r\n        status: 0,\r\n        rejectReason:null,\r\n      }\r\n      this.currentRecord = {}\r\n      this.submissionRecords = []\r\n      this.selectedRecords = []\r\n      this.singleApprovalOpen = false\r\n      this.batchApprovalOpen = false\r\n      this.singleApprovalForm = {\r\n        id: '',\r\n        action: '',\r\n        rejectReason: ''\r\n      }\r\n      this.batchApprovalForm = {\r\n        action: '',\r\n        rejectReason: ''\r\n      }\r\n      this.resetForm(\"form\")\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = []\r\n      this.approvalDateRange = []\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = \"添加VIEW\"\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.currentRecord = row\r\n      this.loadSubmissionRecords(row.litigationCaseId)\r\n      this.open = true\r\n    },\r\n\r\n    /** 批量修改按钮操作 */\r\n    handleBatchEdit() {\r\n      this.$modal.msgError('请选择单条记录进行审批操作')\r\n    },\r\n\r\n    /** 加载费用提交记录 */\r\n    loadSubmissionRecords(litigationCaseId) {\r\n      this.recordsLoading = true\r\n      getLitigationCostSubmissionRecords(litigationCaseId).then(response => {\r\n        this.submissionRecords = response.data || []\r\n        this.recordsLoading = false\r\n      }).catch(() => {\r\n        this.recordsLoading = false\r\n      })\r\n    },\r\n\r\n    /** 记录选择变化 */\r\n    handleRecordSelectionChange(selection) {\r\n      this.selectedRecords = selection\r\n    },\r\n\r\n    /** 单个审批 */\r\n    handleSingleApprove(record, action) {\r\n      console.log('=== 前端单个审批点击 ===')\r\n      console.log('选中的记录:', record)\r\n      console.log('记录ID:', record.id, '类型:', typeof record.id)\r\n      console.log('操作类型:', action)\r\n\r\n      this.singleApprovalForm.id = record.id\r\n      this.singleApprovalForm.action = action\r\n      this.singleApprovalForm.rejectReason = ''\r\n      this.singleApprovalOpen = true\r\n\r\n      console.log('设置后的singleApprovalForm:', this.singleApprovalForm)\r\n    },\r\n\r\n    /** 确认单个审批 */\r\n    confirmSingleApproval() {\r\n      if (this.singleApprovalForm.action === 'reject') {\r\n        this.$refs[\"singleApprovalForm\"].validate(valid => {\r\n          if (!valid) return\r\n          this.executeSingleApproval()\r\n        })\r\n      } else {\r\n        this.executeSingleApproval()\r\n      }\r\n    },\r\n\r\n    /** 执行单个审批 */\r\n    executeSingleApproval() {\r\n      console.log('=== 前端执行单个审批开始 ===')\r\n      console.log('singleApprovalForm:', this.singleApprovalForm)\r\n\r\n      const data = {\r\n        id: this.singleApprovalForm.id,\r\n        action: this.singleApprovalForm.action,\r\n        rejectReason: this.singleApprovalForm.rejectReason\r\n      }\r\n\r\n      console.log('发送到后端的数据:', data)\r\n      console.log('ID类型:', typeof data.id, 'ID值:', data.id)\r\n\r\n      singleApproveLitigationCostNew(data).then(() => {\r\n        console.log('审批成功')\r\n        this.$modal.msgSuccess(`${this.singleApprovalForm.action === 'approve' ? '通过' : '拒绝'}审批成功`)\r\n        this.singleApprovalOpen = false\r\n        this.loadSubmissionRecords(this.currentRecord.litigationCaseId)\r\n        this.getList()\r\n      }).catch((error) => {\r\n        console.error('审批失败:', error)\r\n      })\r\n    },\r\n\r\n    /** 批量审批 */\r\n    handleBatchApprove(action) {\r\n      if (this.selectedRecords.length === 0) {\r\n        this.$modal.msgError('请选择要审批的记录')\r\n        return\r\n      }\r\n\r\n      this.batchApprovalForm.action = action\r\n      this.batchApprovalForm.rejectReason = ''\r\n      this.batchApprovalOpen = true\r\n    },\r\n\r\n    /** 确认批量审批 */\r\n    confirmBatchApproval() {\r\n      if (this.batchApprovalForm.action === 'reject') {\r\n        this.$refs[\"batchApprovalForm\"].validate(valid => {\r\n          if (!valid) return\r\n          this.executeBatchApproval()\r\n        })\r\n      } else {\r\n        this.executeBatchApproval()\r\n      }\r\n    },\r\n\r\n    /** 执行批量审批 */\r\n    executeBatchApproval() {\r\n      const data = {\r\n        ids: this.selectedRecords.map(record => record.id),\r\n        action: this.batchApprovalForm.action,\r\n        rejectReason: this.batchApprovalForm.rejectReason\r\n      }\r\n\r\n      batchApproveLitigationCostNew(data).then(() => {\r\n        this.$modal.msgSuccess(`批量${this.batchApprovalForm.action === 'approve' ? '通过' : '拒绝'}审批成功`)\r\n        this.batchApprovalOpen = false\r\n        this.selectedRecords = []\r\n        this.loadSubmissionRecords(this.currentRecord.litigationCaseId)\r\n        this.getList()\r\n      }).catch((error) => {\r\n        console.error('批量审批失败:', error)\r\n      })\r\n    },\r\n\r\n    /** 主列表批量审批 */\r\n    handleBatchApproveMain(status) {\r\n      if (this.ids.length === 0) {\r\n        this.$modal.msgError('请选择要审批的记录')\r\n        return\r\n      }\r\n\r\n      const statusText = status === '0' ? '通过' : '拒绝'\r\n      this.$modal.confirm(`确认要批量${statusText}选中的 ${this.ids.length} 条记录吗？`).then(() => {\r\n        const data = {\r\n          ids: this.ids,\r\n          status: status,\r\n          rejectReason: status === '1' ? '批量拒绝' : ''\r\n        }\r\n\r\n        return batchApproveLitigationCostRecords(data)\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(`批量${statusText}成功`)\r\n        this.getList()\r\n      }).catch(() => {})\r\n    },\r\n\r\n    /** 删除按钮操作 */\r\n    handleDelete() {\r\n      this.$modal.confirm('是否确认删除选中的数据项？').then(() => {\r\n        // 这里可以调用删除API，暂时只是提示\r\n        this.$modal.msgSuccess(\"删除功能暂未实现\")\r\n      }).catch(() => {})\r\n    },\r\n\r\n\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('litigation_cost_approval/litigation_cost_approval/export', {\r\n        ...this.queryParams\r\n      }, `litigation_cost_approval_${new Date().getTime()}.xlsx`)\r\n    },\r\n\r\n    /** 打开贷款人信息 */\r\n    openUserInfo(row) {\r\n      if (!row.customerId && !row.applyId) {\r\n        this.$modal.msgError('无法获取贷款人信息')\r\n        return\r\n      }\r\n\r\n      this.customerInfo = {\r\n        customerId: row.customerId,\r\n        applyId: row.applyId,\r\n        customerName: row.customerName\r\n      }\r\n      this.userInfoVisible = true\r\n    },\r\n\r\n    /** 处理日期范围变化 */\r\n    handleDateRangeChange(dates) {\r\n      if (dates && dates.length === 2) {\r\n        this.queryParams.startTime = dates[0]\r\n        this.queryParams.endTime = dates[1]\r\n      } else {\r\n        this.queryParams.startTime = null\r\n        this.queryParams.endTime = null\r\n      }\r\n      this.handleQuery()\r\n    },\r\n\r\n    /** 处理审批时间范围变化 */\r\n    handleApprovalDateRangeChange(dates) {\r\n      if (dates && dates.length === 2) {\r\n        this.queryParams.approvalStartTime = dates[0]\r\n        this.queryParams.approvalEndTime = dates[1]\r\n      } else {\r\n        this.queryParams.approvalStartTime = null\r\n        this.queryParams.approvalEndTime = null\r\n      }\r\n      this.handleQuery()\r\n    },\r\n\r\n    /** 获取状态文本 */\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        '0': '未审批',\r\n        '1': '全部同意',\r\n        '2': '已拒绝',\r\n        '3': '法诉主管审批',\r\n        '4': '总监审批',\r\n        '5': '财务主管/总监抄送',\r\n        '6': '总经理/董事长审批'\r\n      }\r\n      return statusMap[status] || '未知状态'\r\n    },\r\n\r\n    /** 检查记录是否可以审批 */\r\n    canApproveRecord(record) {\r\n      // 根据当前用户角色和记录状态判断是否可以审批\r\n      // 这里简化处理，实际应该根据用户角色和审批状态来判断\r\n      return record.approvalStatus === '0' || record.approvalStatus === null || record.approvalStatus === '' ||\r\n             record.approvalStatus === '3' || record.approvalStatus === '4' ||\r\n             record.approvalStatus === '5' || record.approvalStatus === '6'\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.approval-header {\r\n  background-color: #f5f7fa;\r\n  padding: 15px;\r\n  border-radius: 4px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.batch-approval-section {\r\n  border: 1px solid #e4e7ed;\r\n  padding: 10px;\r\n  border-radius: 4px;\r\n  background-color: #fafafa;\r\n}\r\n\r\n.el-table {\r\n  margin-top: 10px;\r\n}\r\n\r\n.el-tag {\r\n  margin: 2px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;AA8ZA,IAAAA,yBAAA,GAAAC,OAAA;AAQA,IAAAC,KAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,SAAA,GAAAD,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAI,IAAA;EACAC,UAAA;IACAC,QAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,gBAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACA;QACA;QACAC,YAAA;QACA;QACAC,MAAA;QACA;QACAC,MAAA;QACA;QACAC,WAAA;QACA;QACAC,gBAAA;QACA;QACAC,aAAA;QACA;QACAC,YAAA;QACA;QACAC,cAAA;QACA;QACAC,SAAA;QACAC,OAAA;QACA;QACAC,iBAAA;QACAC,eAAA;MACA;MACA;MACAC,SAAA;MACA;MACAC,iBAAA;MACA;MACAC,IAAA;QACAC,EAAA;QACAC,MAAA;QACAC,YAAA;MACA;MACA;MACAC,aAAA;MACA;MACAC,iBAAA;MACA;MACAC,cAAA;MACA;MACAC,eAAA;MACA;MACAC,kBAAA;MACAC,kBAAA;QACAR,EAAA;QACAS,MAAA;QAAA;QACAP,YAAA;MACA;MACA;MACAQ,iBAAA;MACAC,iBAAA;QACAF,MAAA;QAAA;QACAP,YAAA;MACA;MACA;MACAU,eAAA;MACAC,YAAA;MACA;MACAC,uBAAA,GACA;QACAC,KAAA;QACAC,KAAA;QACAC,QAAA,GACA;UAAAF,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA;MAEA,GACA;QACAD,KAAA;QACAC,KAAA;QACAC,QAAA,GACA;UAAAF,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA;MAEA,GACA;QACAD,KAAA;QACAC,KAAA;QACAC,QAAA,GACA;UAAAF,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA;MAEA,GACA;QACAD,KAAA;QACAC,KAAA;QACAC,QAAA,GACA;UAAAF,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA;MAEA,EACA;MACA;MACAE,KAAA;QACAC,WAAA;QACAC,OAAA;QACAC,UAAA;QACAC,gBAAA;MACA;MACAC,YAAA,EAAAC,aAAA;MACAC,QAAA;MACAC,YAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,iBACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAzD,OAAA;MACA,IAAA0D,oDAAA,OAAAjD,WAAA,EAAAkD,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAnD,gBAAA,GAAAsD,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAApD,KAAA,GAAAuD,QAAA,CAAAvD,KAAA;QACAoD,KAAA,CAAAzD,OAAA;MACA;IACA;IACA;IACA8D,MAAA,WAAAA,OAAA;MACA,KAAAtD,IAAA;MACA,KAAAuD,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAArC,IAAA;QACAC,EAAA;QACAC,MAAA;QACAC,YAAA;MACA;MACA,KAAAC,aAAA;MACA,KAAAC,iBAAA;MACA,KAAAE,eAAA;MACA,KAAAC,kBAAA;MACA,KAAAG,iBAAA;MACA,KAAAF,kBAAA;QACAR,EAAA;QACAS,MAAA;QACAP,YAAA;MACA;MACA,KAAAS,iBAAA;QACAF,MAAA;QACAP,YAAA;MACA;MACA,KAAAmC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAxD,WAAA,CAAAC,OAAA;MACA,KAAA6C,OAAA;IACA;IACA,aACAW,UAAA,WAAAA,WAAA;MACA,KAAA1C,SAAA;MACA,KAAAC,iBAAA;MACA,KAAAuC,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAnE,GAAA,GAAAmE,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA3C,EAAA;MAAA;MACA,KAAAzB,MAAA,GAAAkE,SAAA,CAAAG,MAAA;MACA,KAAApE,QAAA,IAAAiE,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAT,KAAA;MACA,KAAAvD,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAkE,YAAA,WAAAA,aAAAC,GAAA;MACA,KAAA5C,aAAA,GAAA4C,GAAA;MACA,KAAAC,qBAAA,CAAAD,GAAA,CAAAE,gBAAA;MACA,KAAApE,IAAA;IACA;IAEA,eACAqE,eAAA,WAAAA,gBAAA;MACA,KAAAC,MAAA,CAAAC,QAAA;IACA;IAEA,eACAJ,qBAAA,WAAAA,sBAAAC,gBAAA;MAAA,IAAAI,MAAA;MACA,KAAAhD,cAAA;MACA,IAAAiD,4DAAA,EAAAL,gBAAA,EAAAjB,IAAA,WAAAC,QAAA;QACAoB,MAAA,CAAAjD,iBAAA,GAAA6B,QAAA,CAAA7D,IAAA;QACAiF,MAAA,CAAAhD,cAAA;MACA,GAAAkD,KAAA;QACAF,MAAA,CAAAhD,cAAA;MACA;IACA;IAEA,aACAmD,2BAAA,WAAAA,4BAAAf,SAAA;MACA,KAAAnC,eAAA,GAAAmC,SAAA;IACA;IAEA,WACAgB,mBAAA,WAAAA,oBAAAC,MAAA,EAAAjD,MAAA;MACAkD,OAAA,CAAAC,GAAA;MACAD,OAAA,CAAAC,GAAA,WAAAF,MAAA;MACAC,OAAA,CAAAC,GAAA,UAAAF,MAAA,CAAA1D,EAAA,aAAA6D,QAAA,CAAAC,OAAA,EAAAJ,MAAA,CAAA1D,EAAA;MACA2D,OAAA,CAAAC,GAAA,UAAAnD,MAAA;MAEA,KAAAD,kBAAA,CAAAR,EAAA,GAAA0D,MAAA,CAAA1D,EAAA;MACA,KAAAQ,kBAAA,CAAAC,MAAA,GAAAA,MAAA;MACA,KAAAD,kBAAA,CAAAN,YAAA;MACA,KAAAK,kBAAA;MAEAoD,OAAA,CAAAC,GAAA,iCAAApD,kBAAA;IACA;IAEA,aACAuD,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MACA,SAAAxD,kBAAA,CAAAC,MAAA;QACA,KAAAwD,KAAA,uBAAAC,QAAA,WAAAC,KAAA;UACA,KAAAA,KAAA;UACAH,MAAA,CAAAI,qBAAA;QACA;MACA;QACA,KAAAA,qBAAA;MACA;IACA;IAEA,aACAA,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MACAV,OAAA,CAAAC,GAAA;MACAD,OAAA,CAAAC,GAAA,6BAAApD,kBAAA;MAEA,IAAApC,IAAA;QACA4B,EAAA,OAAAQ,kBAAA,CAAAR,EAAA;QACAS,MAAA,OAAAD,kBAAA,CAAAC,MAAA;QACAP,YAAA,OAAAM,kBAAA,CAAAN;MACA;MAEAyD,OAAA,CAAAC,GAAA,cAAAxF,IAAA;MACAuF,OAAA,CAAAC,GAAA,cAAAC,QAAA,CAAAC,OAAA,EAAA1F,IAAA,CAAA4B,EAAA,WAAA5B,IAAA,CAAA4B,EAAA;MAEA,IAAAsE,wDAAA,EAAAlG,IAAA,EAAA4D,IAAA;QACA2B,OAAA,CAAAC,GAAA;QACAS,MAAA,CAAAlB,MAAA,CAAAoB,UAAA,IAAAC,MAAA,CAAAH,MAAA,CAAA7D,kBAAA,CAAAC,MAAA;QACA4D,MAAA,CAAA9D,kBAAA;QACA8D,MAAA,CAAArB,qBAAA,CAAAqB,MAAA,CAAAlE,aAAA,CAAA8C,gBAAA;QACAoB,MAAA,CAAAzC,OAAA;MACA,GAAA2B,KAAA,WAAAkB,KAAA;QACAd,OAAA,CAAAc,KAAA,UAAAA,KAAA;MACA;IACA;IAEA,WACAC,kBAAA,WAAAA,mBAAAjE,MAAA;MACA,SAAAH,eAAA,CAAAsC,MAAA;QACA,KAAAO,MAAA,CAAAC,QAAA;QACA;MACA;MAEA,KAAAzC,iBAAA,CAAAF,MAAA,GAAAA,MAAA;MACA,KAAAE,iBAAA,CAAAT,YAAA;MACA,KAAAQ,iBAAA;IACA;IAEA,aACAiE,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA,SAAAjE,iBAAA,CAAAF,MAAA;QACA,KAAAwD,KAAA,sBAAAC,QAAA,WAAAC,KAAA;UACA,KAAAA,KAAA;UACAS,MAAA,CAAAC,oBAAA;QACA;MACA;QACA,KAAAA,oBAAA;MACA;IACA;IAEA,aACAA,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA,IAAA1G,IAAA;QACAE,GAAA,OAAAgC,eAAA,CAAAoC,GAAA,WAAAgB,MAAA;UAAA,OAAAA,MAAA,CAAA1D,EAAA;QAAA;QACAS,MAAA,OAAAE,iBAAA,CAAAF,MAAA;QACAP,YAAA,OAAAS,iBAAA,CAAAT;MACA;MAEA,IAAA6E,uDAAA,EAAA3G,IAAA,EAAA4D,IAAA;QACA8C,MAAA,CAAA3B,MAAA,CAAAoB,UAAA,gBAAAC,MAAA,CAAAM,MAAA,CAAAnE,iBAAA,CAAAF,MAAA;QACAqE,MAAA,CAAApE,iBAAA;QACAoE,MAAA,CAAAxE,eAAA;QACAwE,MAAA,CAAA9B,qBAAA,CAAA8B,MAAA,CAAA3E,aAAA,CAAA8C,gBAAA;QACA6B,MAAA,CAAAlD,OAAA;MACA,GAAA2B,KAAA,WAAAkB,KAAA;QACAd,OAAA,CAAAc,KAAA,YAAAA,KAAA;MACA;IACA;IAEA,cACAO,sBAAA,WAAAA,uBAAA/E,MAAA;MAAA,IAAAgF,MAAA;MACA,SAAA3G,GAAA,CAAAsE,MAAA;QACA,KAAAO,MAAA,CAAAC,QAAA;QACA;MACA;MAEA,IAAA8B,UAAA,GAAAjF,MAAA;MACA,KAAAkD,MAAA,CAAAgC,OAAA,kCAAAX,MAAA,CAAAU,UAAA,yBAAAV,MAAA,MAAAlG,GAAA,CAAAsE,MAAA,sCAAAZ,IAAA;QACA,IAAA5D,IAAA;UACAE,GAAA,EAAA2G,MAAA,CAAA3G,GAAA;UACA2B,MAAA,EAAAA,MAAA;UACAC,YAAA,EAAAD,MAAA;QACA;QAEA,WAAAmF,2DAAA,EAAAhH,IAAA;MACA,GAAA4D,IAAA;QACAiD,MAAA,CAAA9B,MAAA,CAAAoB,UAAA,gBAAAC,MAAA,CAAAU,UAAA;QACAD,MAAA,CAAArD,OAAA;MACA,GAAA2B,KAAA;IACA;IAEA,aACA8B,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAnC,MAAA,CAAAgC,OAAA,kBAAAnD,IAAA;QACA;QACAsD,MAAA,CAAAnC,MAAA,CAAAoB,UAAA;MACA,GAAAhB,KAAA;IACA;IAGA,aACAgC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,iEAAAC,cAAA,CAAA3B,OAAA,MACA,KAAAhF,WAAA,+BAAA0F,MAAA,CACA,IAAAkB,IAAA,GAAAC,OAAA;IACA;IAEA,cACAC,YAAA,WAAAA,aAAA7C,GAAA;MACA,KAAAA,GAAA,CAAA8C,UAAA,KAAA9C,GAAA,CAAA+C,OAAA;QACA,KAAA3C,MAAA,CAAAC,QAAA;QACA;MACA;MAEA,KAAAvC,YAAA;QACAgF,UAAA,EAAA9C,GAAA,CAAA8C,UAAA;QACAC,OAAA,EAAA/C,GAAA,CAAA+C,OAAA;QACA7G,YAAA,EAAA8D,GAAA,CAAA9D;MACA;MACA,KAAA2B,eAAA;IACA;IAEA,eACAmF,qBAAA,WAAAA,sBAAAC,KAAA;MACA,IAAAA,KAAA,IAAAA,KAAA,CAAApD,MAAA;QACA,KAAA9D,WAAA,CAAAW,SAAA,GAAAuG,KAAA;QACA,KAAAlH,WAAA,CAAAY,OAAA,GAAAsG,KAAA;MACA;QACA,KAAAlH,WAAA,CAAAW,SAAA;QACA,KAAAX,WAAA,CAAAY,OAAA;MACA;MACA,KAAA4C,WAAA;IACA;IAEA,iBACA2D,6BAAA,WAAAA,8BAAAD,KAAA;MACA,IAAAA,KAAA,IAAAA,KAAA,CAAApD,MAAA;QACA,KAAA9D,WAAA,CAAAa,iBAAA,GAAAqG,KAAA;QACA,KAAAlH,WAAA,CAAAc,eAAA,GAAAoG,KAAA;MACA;QACA,KAAAlH,WAAA,CAAAa,iBAAA;QACA,KAAAb,WAAA,CAAAc,eAAA;MACA;MACA,KAAA0C,WAAA;IACA;IAEA,aACA4D,aAAA,WAAAA,cAAAjG,MAAA;MACA,IAAAkG,SAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAlG,MAAA;IACA;IAEA,iBACAmG,gBAAA,WAAAA,iBAAA1C,MAAA;MACA;MACA;MACA,OAAAA,MAAA,CAAAlE,cAAA,YAAAkE,MAAA,CAAAlE,cAAA,aAAAkE,MAAA,CAAAlE,cAAA,WACAkE,MAAA,CAAAlE,cAAA,YAAAkE,MAAA,CAAAlE,cAAA,YACAkE,MAAA,CAAAlE,cAAA,YAAAkE,MAAA,CAAAlE,cAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}