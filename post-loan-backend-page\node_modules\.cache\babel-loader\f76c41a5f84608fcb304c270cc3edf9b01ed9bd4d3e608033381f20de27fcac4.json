{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _objectSpread2 = _interopRequireDefault(require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/objectSpread2.js\"));\nrequire(\"core-js/modules/es.array.map.js\");\nrequire(\"core-js/modules/es.object.to-string.js\");\nrequire(\"core-js/modules/esnext.iterator.constructor.js\");\nrequire(\"core-js/modules/esnext.iterator.map.js\");\nvar _vw_car_order_examine = require(\"@/api/vw_car_order_examine/vw_car_order_examine\");\nvar _userInfo = _interopRequireDefault(require(\"@/layout/components/Dialog/userInfo.vue\"));\nvar _carInfo = _interopRequireDefault(require(\"@/layout/components/Dialog/carInfo.vue\"));\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: 'Vw_car_order_examine',\n  components: {\n    userInfo: _userInfo.default,\n    carInfo: _carInfo.default\n  },\n  data: function data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 找车费用审批表格数据\n      vw_car_order_examineList: [],\n      // 弹出层标题\n      title: '',\n      // 是否显示弹出层\n      open: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 15,\n        teamName: null,\n        keyStatus: null,\n        originallyTime: null,\n        startTime: '',\n        endTime: '',\n        customerName: null,\n        plateNo: null,\n        jgName: null,\n        garageName: null\n      },\n      // 表单参数\n      form: {\n        id: '',\n        status: 0,\n        newStatus: null,\n        rejectReason: null,\n        customerName: '',\n        customerId: '',\n        applyId: '',\n        plateNo: '',\n        jgName: '',\n        teamName: '',\n        allocationTime: '',\n        keyStatus: '',\n        totalMoney: '',\n        _readonly: false\n      },\n      // 表单校验\n      rules: {\n        id: [{\n          required: true,\n          message: '$comment不能为空',\n          trigger: 'blur'\n        }]\n      },\n      jgNameList: [{\n        label: 'A公司',\n        value: 1\n      }, {\n        label: 'B公司',\n        value: 2\n      }],\n      keyStatusList: [{\n        label: '已邮寄',\n        value: 1\n      }, {\n        label: '已收回',\n        value: 2\n      }, {\n        label: '已归还',\n        value: 3\n      }],\n      teamList: [{\n        label: 'A团队',\n        value: 1\n      }, {\n        label: 'B团队',\n        value: 2\n      }],\n      customerInfo: {\n        customerId: '',\n        applyId: ''\n      },\n      userInfoVisible: false,\n      plateNo: '',\n      carInfoVisible: false,\n      // 详情对话框显示状态\n      detailsDialogVisible: false,\n      // 当前订单信息\n      currentOrderInfo: null,\n      // 费用记录列表\n      feeRecords: [],\n      // 费用记录加载状态\n      recordsLoading: false,\n      // 选中的费用记录\n      selectedRecords: [],\n      // 单个审批对话框\n      singleApprovalDialogVisible: false,\n      singleApprovalForm: {\n        id: '',\n        action: '',\n        // 'approve' 或 'reject'\n        rejectReason: ''\n      },\n      // 批量审批对话框\n      batchApprovalDialogVisible: false,\n      batchApprovalForm: {\n        action: '',\n        // 'approve' 或 'reject'\n        rejectReason: ''\n      }\n    };\n  },\n  created: function created() {\n    this.getTeam();\n    this.getList();\n  },\n  methods: {\n    // 查询录单渠道、找车团队\n    getTeam: function getTeam() {\n      var _this = this;\n      (0, _vw_car_order_examine.teamVm_car_order)().then(function (response) {\n        _this.teamList = response.team;\n        _this.jgNameList = response.office;\n      });\n    },\n    /** 查询找车费用审批列表 */getList: function getList() {\n      var _this2 = this;\n      this.loading = true;\n      (0, _vw_car_order_examine.listVw_car_order_examine)(this.queryParams).then(function (response) {\n        _this2.vw_car_order_examineList = response.rows;\n        _this2.total = response.total;\n        _this2.loading = false;\n      });\n    },\n    // 取消按钮\n    cancel: function cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset: function reset() {\n      this.form = {\n        id: '',\n        status: 0,\n        newStatus: null,\n        rejectReason: null,\n        customerName: '',\n        customerId: '',\n        applyId: '',\n        plateNo: '',\n        jgName: '',\n        teamName: '',\n        allocationTime: '',\n        keyStatus: '',\n        totalMoney: '',\n        _readonly: false\n      };\n      this.resetForm('form');\n    },\n    /** 搜索按钮操作 */handleQuery: function handleQuery() {\n      if (this.queryParams.originallyTime) {\n        this.queryParams.startTime = this.queryParams.originallyTime[0];\n        this.queryParams.endTime = this.queryParams.originallyTime[1];\n      }\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */resetQuery: function resetQuery() {\n      this.queryParams.customerName = null;\n      this.queryParams.plateNo = null;\n      this.queryParams.jgName = null;\n      this.queryParams.keyStatus = null;\n      this.queryParams.teamName = null;\n      this.queryParams.originallyTime = null;\n      this.queryParams.startTime = null;\n      this.queryParams.endTime = null;\n      this.queryParams.garageName = null;\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange: function handleSelectionChange(selection) {\n      this.ids = selection.map(function (item) {\n        return item.id;\n      });\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    /** 新增按钮操作 */handleAdd: function handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = '添加找车费用审批';\n    },\n    /** 修改按钮操作 */handleUpdate: function handleUpdate(row) {\n      // this.reset()\n      this.form.id = row.id;\n      this.form.rejectReason = row.rejectReason;\n      this.form.status = row.status;\n      // const id = row.id || this.ids\n      // getVw_car_order_examine(id).then(response => {\n      //   // this.form = response.data\n\n      // })\n      this.open = true;\n      this.title = '找车费用审批';\n    },\n    // 获取状态文本\n    getStatusText: function getStatusText(status) {\n      var statusMap = {\n        0: '未审批',\n        1: '全部通过',\n        2: '已拒绝',\n        3: '贷后主管审批',\n        4: '总监审批',\n        5: '财务主管/总监抄送',\n        6: '总经理/董事长审批(抄送)'\n      };\n      return statusMap[status] || '未知状态';\n    },\n    /** 删除按钮操作 */handleDelete: function handleDelete(row) {\n      var _this3 = this;\n      var ids = row.id || this.ids;\n      this.$modal.confirm('是否确认删除找车费用审批编号为\"' + ids + '\"的数据项？').then(function () {\n        return (0, _vw_car_order_examine.delVw_car_order_examine)(ids);\n      }).then(function () {\n        _this3.getList();\n        _this3.$modal.msgSuccess('删除成功');\n      }).catch(function () {});\n    },\n    /** 导出按钮操作 */handleExport: function handleExport() {\n      this.download('vw_car_order_examine/vw_car_order_examine/export', (0, _objectSpread2.default)({}, this.queryParams), \"vw_car_order_examine_\".concat(new Date().getTime(), \".xlsx\"));\n    },\n    openUserInfo: function openUserInfo(customerInfo) {\n      console.log('点击客户信息:', customerInfo);\n      if (!customerInfo.customerId || !customerInfo.applyId) {\n        this.$modal.msgError('客户信息不完整，无法查看详情');\n        return;\n      }\n      this.customerInfo = customerInfo;\n      this.userInfoVisible = true;\n    },\n    openCarInfo: function openCarInfo(plateNo) {\n      this.plateNo = plateNo;\n      this.carInfoVisible = true;\n    },\n    /** 查看费用详情和审批 */handleViewDetails: function handleViewDetails(row) {\n      this.currentOrderInfo = row;\n      this.detailsDialogVisible = true;\n      this.loadFeeRecords(row.orderId);\n    },\n    /** 加载费用记录 */loadFeeRecords: function loadFeeRecords(orderId) {\n      var _this4 = this;\n      this.recordsLoading = true;\n      (0, _vw_car_order_examine.getCarOrderExamineRecords)(orderId).then(function (response) {\n        _this4.feeRecords = response.data || [];\n        _this4.recordsLoading = false;\n      }).catch(function (error) {\n        _this4.recordsLoading = false;\n        console.error('加载费用记录失败:', error);\n        _this4.$modal.msgError('加载费用记录失败');\n      });\n    },\n    /** 费用记录选择变化 */handleRecordSelectionChange: function handleRecordSelectionChange(selection) {\n      this.selectedRecords = selection;\n    },\n    /** 单个审批 */handleSingleApprove: function handleSingleApprove(record, action) {\n      this.singleApprovalForm.id = record.id;\n      this.singleApprovalForm.action = action;\n      this.singleApprovalForm.rejectReason = '';\n      this.singleApprovalDialogVisible = true;\n    },\n    /** 确认单个审批 */confirmSingleApproval: function confirmSingleApproval() {\n      var _this5 = this;\n      if (this.singleApprovalForm.action === 'reject') {\n        this.$refs[\"singleApprovalForm\"].validate(function (valid) {\n          if (!valid) return;\n          _this5.executeSingleApproval();\n        });\n      } else {\n        this.executeSingleApproval();\n      }\n    },\n    /** 执行单个审批 */executeSingleApproval: function executeSingleApproval() {\n      var _this6 = this;\n      var data = {\n        id: this.singleApprovalForm.id,\n        action: this.singleApprovalForm.action,\n        rejectReason: this.singleApprovalForm.rejectReason\n      };\n      (0, _vw_car_order_examine.singleApproveCarOrderExamine)(data).then(function () {\n        _this6.$modal.msgSuccess(\"\".concat(_this6.singleApprovalForm.action === 'approve' ? '通过' : '拒绝', \"\\u5BA1\\u6279\\u6210\\u529F\"));\n        _this6.singleApprovalDialogVisible = false;\n        _this6.loadFeeRecords(_this6.currentOrderInfo.id);\n        _this6.getList();\n      }).catch(function (error) {\n        // 不显示通用错误提示，因为 request.js 已经处理了错误显示\n        console.error('审批失败:', error);\n      });\n    },\n    /** 批量审批 */handleBatchApprove: function handleBatchApprove(action) {\n      if (this.selectedRecords.length === 0) {\n        this.$modal.msgError('请选择要审批的记录');\n        return;\n      }\n      this.batchApprovalForm.action = action;\n      this.batchApprovalForm.rejectReason = '';\n      this.batchApprovalDialogVisible = true;\n    },\n    /** 确认批量审批 */confirmBatchApproval: function confirmBatchApproval() {\n      var _this7 = this;\n      if (this.batchApprovalForm.action === 'reject') {\n        this.$refs[\"batchApprovalForm\"].validate(function (valid) {\n          if (!valid) return;\n          _this7.executeBatchApproval();\n        });\n      } else {\n        this.executeBatchApproval();\n      }\n    },\n    /** 执行批量审批 */executeBatchApproval: function executeBatchApproval() {\n      var _this8 = this;\n      var data = {\n        ids: this.selectedRecords.map(function (record) {\n          return record.id;\n        }),\n        action: this.batchApprovalForm.action,\n        rejectReason: this.batchApprovalForm.rejectReason\n      };\n      (0, _vw_car_order_examine.batchApproveCarOrderExamine)(data).then(function () {\n        _this8.$modal.msgSuccess(\"\\u6279\\u91CF\".concat(_this8.batchApprovalForm.action === 'approve' ? '通过' : '拒绝', \"\\u5BA1\\u6279\\u6210\\u529F\"));\n        _this8.batchApprovalDialogVisible = false;\n        _this8.loadFeeRecords(_this8.currentOrderInfo.id);\n        _this8.getList();\n      }).catch(function (error) {\n        // 不显示通用错误提示，因为 request.js 已经处理了错误显示\n        console.error('批量审批失败:', error);\n      });\n    },\n    /** 检查记录是否可以审批 */canApproveRecord: function canApproveRecord(record) {\n      // 只有未审批的记录可以审批\n      return record.status === 0 || record.status === null || record.status === '';\n    },\n    /** 获取状态标签类型 */getStatusTagType: function getStatusTagType(status) {\n      switch (status) {\n        case 0:\n          return 'info';\n        case 1:\n          return 'success';\n        case 7:\n          return 'danger';\n        case 3:\n        case 4:\n        case 5:\n        case 6:\n          return 'warning';\n        default:\n          return 'info';\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["_vw_car_order_examine", "require", "_userInfo", "_interopRequireDefault", "_carInfo", "name", "components", "userInfo", "carInfo", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "vw_car_order_examineList", "title", "open", "queryParams", "pageNum", "pageSize", "teamName", "keyStatus", "originallyTime", "startTime", "endTime", "customerName", "plateNo", "jgName", "garageName", "form", "id", "status", "newStatus", "rejectReason", "customerId", "applyId", "allocationTime", "totalMoney", "_readonly", "rules", "required", "message", "trigger", "jgNameList", "label", "value", "keyStatusList", "teamList", "customerInfo", "userInfoVisible", "carInfoVisible", "detailsDialogVisible", "currentOrderInfo", "feeRecords", "recordsLoading", "selected<PERSON><PERSON><PERSON><PERSON>", "singleApprovalDialogVisible", "singleApprovalForm", "action", "batchApprovalDialogVisible", "batchApprovalForm", "created", "getTeam", "getList", "methods", "_this", "teamVm_car_order", "then", "response", "team", "office", "_this2", "listVw_car_order_examine", "rows", "cancel", "reset", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "getStatusText", "statusMap", "handleDelete", "_this3", "$modal", "confirm", "delVw_car_order_examine", "msgSuccess", "catch", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime", "openUserInfo", "console", "log", "msgError", "openCarInfo", "handleViewDetails", "loadFeeRecords", "orderId", "_this4", "getCarOrderExamineRecords", "error", "handleRecordSelectionChange", "handleSingleApprove", "record", "confirmSingleApproval", "_this5", "$refs", "validate", "valid", "executeSingleApproval", "_this6", "singleApproveCarOrderExamine", "handleBatchApprove", "confirmBatchApproval", "_this7", "executeBatchApproval", "_this8", "batchApproveCarOrderExamine", "canApproveRecord", "getStatusTagType"], "sources": ["src/views/vw_car_order_examine/vw_car_order_examine/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"\" prop=\"customerName\">\r\n        <el-input v-model=\"queryParams.customerName\" placeholder=\"贷款人账户、姓名\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"plateNo\">\r\n        <el-input v-model=\"queryParams.plateNo\" placeholder=\"请输入车牌号\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"jgName\">\r\n        <el-input v-model=\"queryParams.jgName\" placeholder=\"录入渠道名称\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"garageName\">\r\n        <el-input v-model=\"queryParams.garageName\" placeholder=\"请输入车库名称\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"keyStatus\">\r\n        <el-select v-model=\"queryParams.keyStatus\" placeholder=\"请选择钥匙状态\" clearable>\r\n          <el-option v-for=\"dict in keyStatusList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"teamName\">\r\n        <el-input v-model=\"queryParams.teamName\" placeholder=\"找车团队\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"派单时间\">\r\n        <el-date-picker\r\n          v-model=\"queryParams.originallyTime\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"></el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"vw_car_order_examineList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"序号\" align=\"center\" type=\"index\" width=\"55\" fixed=\"left\" />\r\n      <el-table-column label=\"贷款人\" align=\"center\" prop=\"customerName\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            v-if=\"scope.row.customerId && scope.row.applyId\"\r\n            type=\"text\"\r\n            @click=\"openUserInfo({ customerId: scope.row.customerId, applyId: scope.row.applyId })\">\r\n            {{ scope.row.customerName }}\r\n          </el-button>\r\n          <span v-else>{{ scope.row.customerName }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"联系电话\" align=\"center\" prop=\"mobilePhone\" />\r\n      <!-- 出单渠道 -->\r\n      <el-table-column label=\"出单渠道\" align=\"center\" prop=\"jgName\"></el-table-column>\r\n      <el-table-column label=\"车牌号\" align=\"center\" prop=\"plateNo\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button type=\"text\" @click=\"openCarInfo(scope.row.plateNo)\">{{ scope.row.plateNo }}</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"接单团队\" align=\"center\" prop=\"teamName\" />\r\n      <el-table-column label=\"派单时间\" align=\"center\" prop=\"allocationTime\" width=\"180\"></el-table-column>\r\n      <el-table-column label=\"钥匙状态\" align=\"center\" prop=\"keyStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ scope.row.keyStatus == 1 ? '已邮寄' : scope.row.keyStatus == 2 ? '已收回' : '未归还' }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"入库时间\" align=\"center\" prop=\"inboundTime\" width=\"180\"></el-table-column>\r\n      <el-table-column label=\"找车佣金\" align=\"center\" prop=\"locatingCommission\" />\r\n      <el-table-column label=\"佣金\" align=\"center\" prop=\"transportationFee\" />\r\n      <el-table-column label=\"拖车费\" align=\"center\" prop=\"towingFee\" />\r\n      <el-table-column label=\"贴机费\" align=\"center\" prop=\"trackerInstallationFee\" />\r\n      <el-table-column label=\"其他报销\" align=\"center\" prop=\"otherReimbursement\" />\r\n      <el-table-column label=\"合计费用\" align=\"center\" prop=\"totalMoney\" />\r\n      <el-table-column label=\"审批状态\" align=\"center\" prop=\"status\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ getStatusText(scope.row.status) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" fixed=\"right\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            @click=\"handleViewDetails(scope.row)\"\r\n            v-hasPermi=\"['vw_car_order_examine:vw_car_order_examine:edit']\">\r\n            审批\r\n          </el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\" />\r\n\r\n    <!-- 费用详情审批对话框 -->\r\n    <el-dialog title=\"找车费用审批详情\" :visible.sync=\"detailsDialogVisible\" width=\"1200px\" append-to-body>\r\n      <!-- 订单基本信息头部 -->\r\n      <div class=\"approval-header\">\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <strong>贷款人：</strong>\r\n            <el-button\r\n              v-if=\"currentOrderInfo && currentOrderInfo.customerId && currentOrderInfo.applyId\"\r\n              type=\"text\"\r\n              @click=\"openUserInfo({ customerId: currentOrderInfo.customerId, applyId: currentOrderInfo.applyId })\"\r\n              style=\"color: #409EFF;\">\r\n              {{ currentOrderInfo.customerName }}\r\n            </el-button>\r\n            <span v-else>{{ currentOrderInfo ? currentOrderInfo.customerName : '' }}</span>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <strong>接单团队：</strong>{{ currentOrderInfo ? currentOrderInfo.teamName : '' }}\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <strong>车牌号：</strong>\r\n            <el-button\r\n              v-if=\"currentOrderInfo && currentOrderInfo.plateNo\"\r\n              type=\"text\"\r\n              @click=\"openCarInfo(currentOrderInfo.plateNo)\"\r\n              style=\"color: #409EFF;\">\r\n              {{ currentOrderInfo.plateNo }}\r\n            </el-button>\r\n            <span v-else>{{ currentOrderInfo ? currentOrderInfo.plateNo : '' }}</span>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row style=\"margin-top: 10px;\">\r\n          <el-col :span=\"8\">\r\n            <strong>出单渠道：</strong>{{ currentOrderInfo ? currentOrderInfo.jgName : '' }}\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <strong>派单时间：</strong>{{ currentOrderInfo ? currentOrderInfo.allocationTime : '' }}\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <strong>钥匙状态：</strong>\r\n            <span v-if=\"currentOrderInfo\">{{ currentOrderInfo.keyStatus == 1 ? '已邮寄' : currentOrderInfo.keyStatus == 2 ? '已收回' : '未归还' }}</span>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <!-- 批量操作区域 -->\r\n      <div class=\"batch-approval-section\" style=\"margin: 20px 0;\">\r\n        <el-button\r\n          type=\"success\"\r\n          size=\"small\"\r\n          :disabled=\"selectedRecords.length === 0\"\r\n          @click=\"handleBatchApprove('approve')\">\r\n          批量通过 ({{ selectedRecords.length }})\r\n        </el-button>\r\n        <el-button\r\n          type=\"danger\"\r\n          size=\"small\"\r\n          :disabled=\"selectedRecords.length === 0\"\r\n          @click=\"handleBatchApprove('reject')\">\r\n          批量拒绝 ({{ selectedRecords.length }})\r\n        </el-button>\r\n      </div>\r\n\r\n      <el-table\r\n        :data=\"feeRecords\"\r\n        @selection-change=\"handleRecordSelectionChange\"\r\n        v-loading=\"recordsLoading\">\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" fixed=\"left\" />\r\n        <el-table-column label=\"提交时间\" align=\"center\" prop=\"applicationTime\" width=\"150\" />\r\n        <el-table-column label=\"提交人\" align=\"center\" prop=\"applicationBy\" width=\"100\" />\r\n        <el-table-column label=\"佣金\" align=\"center\" prop=\"transportationFee\" width=\"80\" />\r\n        <el-table-column label=\"拖车费\" align=\"center\" prop=\"towingFee\" width=\"80\" />\r\n        <el-table-column label=\"贴机费\" align=\"center\" prop=\"trackerInstallationFee\" width=\"80\" />\r\n        <el-table-column label=\"其他报销\" align=\"center\" prop=\"otherReimbursement\" width=\"80\" />\r\n        <el-table-column label=\"合计费用\" align=\"center\" prop=\"totalMoney\" width=\"100\" />\r\n        <el-table-column label=\"审批状态\" align=\"center\" prop=\"status\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag v-if=\"scope.row.status == null || scope.row.status == 0\" type=\"info\">未审核</el-tag>\r\n            <el-tag v-else-if=\"scope.row.status == 1\" type=\"success\">已通过</el-tag>\r\n            <el-tag v-else-if=\"scope.row.status == 2\" type=\"danger\">已拒绝</el-tag>\r\n            <el-tag v-else type=\"warning\">{{ getStatusText(scope.row.status) }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"拒绝原因\" align=\"center\" prop=\"reasons\" width=\"150\" />\r\n        <el-table-column label=\"操作\" align=\"center\" width=\"150\" fixed=\"right\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              v-if=\"canApproveRecord(scope.row)\"\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"handleSingleApprove(scope.row, 'approve')\">\r\n              通过\r\n            </el-button>\r\n            <el-button\r\n              v-if=\"canApproveRecord(scope.row)\"\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"handleSingleApprove(scope.row, 'reject')\">\r\n              拒绝\r\n            </el-button>\r\n            <div v-else>\r\n              <el-tag v-if=\"scope.row.status == 1\" type=\"success\" size=\"mini\">已通过</el-tag>\r\n              <el-tag v-else-if=\"scope.row.status == 7\" type=\"danger\" size=\"mini\">已拒绝</el-tag>\r\n              <div v-if=\"scope.row.approveBy\" style=\"font-size: 12px; color: #999; margin-top: 2px;\">\r\n                {{ scope.row.approveBy }}\r\n              </div>\r\n              <div v-if=\"scope.row.approveTime\" style=\"font-size: 12px; color: #999;\">\r\n                {{ scope.row.approveTime }}\r\n              </div>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"detailsDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 单个审批确认对话框 -->\r\n    <el-dialog title=\"审批确认\" :visible.sync=\"singleApprovalDialogVisible\" width=\"400px\" append-to-body>\r\n      <el-form ref=\"singleApprovalForm\" :model=\"singleApprovalForm\" label-width=\"80px\">\r\n        <el-form-item label=\"审批结果\">\r\n          <el-tag :type=\"singleApprovalForm.action === 'approve' ? 'success' : 'danger'\">\r\n            {{ singleApprovalForm.action === 'approve' ? '通过' : '拒绝' }}\r\n          </el-tag>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"拒绝原因\"\r\n          prop=\"rejectReason\"\r\n          v-if=\"singleApprovalForm.action === 'reject'\"\r\n          :rules=\"[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]\">\r\n          <el-input\r\n            v-model=\"singleApprovalForm.rejectReason\"\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入拒绝原因\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"confirmSingleApproval\">确 定</el-button>\r\n        <el-button @click=\"singleApprovalDialogVisible = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批量审批确认对话框 -->\r\n    <el-dialog title=\"批量审批确认\" :visible.sync=\"batchApprovalDialogVisible\" width=\"400px\" append-to-body>\r\n      <el-form ref=\"batchApprovalForm\" :model=\"batchApprovalForm\" label-width=\"80px\">\r\n        <el-form-item label=\"审批结果\">\r\n          <el-tag :type=\"batchApprovalForm.action === 'approve' ? 'success' : 'danger'\">\r\n            {{ batchApprovalForm.action === 'approve' ? '批量通过' : '批量拒绝' }}\r\n          </el-tag>\r\n        </el-form-item>\r\n        <el-form-item label=\"选中记录\">\r\n          <span>{{ selectedRecords.length }} 条记录</span>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"拒绝原因\"\r\n          prop=\"rejectReason\"\r\n          v-if=\"batchApprovalForm.action === 'reject'\"\r\n          :rules=\"[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]\">\r\n          <el-input\r\n            v-model=\"batchApprovalForm.rejectReason\"\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入拒绝原因\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"confirmBatchApproval\">确 定</el-button>\r\n        <el-button @click=\"batchApprovalDialogVisible = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 贷款人信息组件 -->\r\n    <userInfo ref=\"userInfo\" :visible.sync=\"userInfoVisible\" title=\"贷款人信息\" :customerInfo=\"customerInfo\" />\r\n    <!-- 车辆信息组件 -->\r\n    <carInfo ref=\"carInfo\" :visible.sync=\"carInfoVisible\" title=\"车辆信息\" :plateNo=\"plateNo\" permission=\"2\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  teamVm_car_order,\r\n  listVw_car_order_examine,\r\n  delVw_car_order_examine,\r\n  getCarOrderExamineRecords,\r\n  batchApproveCarOrderExamine,\r\n  singleApproveCarOrderExamine\r\n} from '@/api/vw_car_order_examine/vw_car_order_examine'\r\nimport userInfo from '@/layout/components/Dialog/userInfo.vue'\r\nimport carInfo from '@/layout/components/Dialog/carInfo.vue'\r\n\r\nexport default {\r\n  name: 'Vw_car_order_examine',\r\n  components: {\r\n    userInfo,\r\n    carInfo,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 找车费用审批表格数据\r\n      vw_car_order_examineList: [],\r\n      // 弹出层标题\r\n      title: '',\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 15,\r\n        teamName: null,\r\n        keyStatus: null,\r\n        originallyTime: null,\r\n        startTime: '',\r\n        endTime: '',\r\n        customerName: null,\r\n        plateNo: null,\r\n        jgName: null,\r\n        garageName: null,\r\n      },\r\n      // 表单参数\r\n      form: {\r\n        id: '',\r\n        status: 0,\r\n        newStatus: null,\r\n        rejectReason: null,\r\n        customerName: '',\r\n        customerId: '',\r\n        applyId: '',\r\n        plateNo: '',\r\n        jgName: '',\r\n        teamName: '',\r\n        allocationTime: '',\r\n        keyStatus: '',\r\n        totalMoney: '',\r\n        _readonly: false,\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        id: [{ required: true, message: '$comment不能为空', trigger: 'blur' }],\r\n      },\r\n      jgNameList: [\r\n        { label: 'A公司', value: 1 },\r\n        { label: 'B公司', value: 2 },\r\n      ],\r\n      keyStatusList: [\r\n        { label: '已邮寄', value: 1 },\r\n        { label: '已收回', value: 2 },\r\n        { label: '已归还', value: 3 },\r\n      ],\r\n      teamList: [\r\n        { label: 'A团队', value: 1 },\r\n        { label: 'B团队', value: 2 },\r\n      ],\r\n      customerInfo: { customerId: '', applyId: '' },\r\n      userInfoVisible: false,\r\n      plateNo: '',\r\n      carInfoVisible: false,\r\n      // 详情对话框显示状态\r\n      detailsDialogVisible: false,\r\n      // 当前订单信息\r\n      currentOrderInfo: null,\r\n      // 费用记录列表\r\n      feeRecords: [],\r\n      // 费用记录加载状态\r\n      recordsLoading: false,\r\n      // 选中的费用记录\r\n      selectedRecords: [],\r\n      // 单个审批对话框\r\n      singleApprovalDialogVisible: false,\r\n      singleApprovalForm: {\r\n        id: '',\r\n        action: '', // 'approve' 或 'reject'\r\n        rejectReason: ''\r\n      },\r\n      // 批量审批对话框\r\n      batchApprovalDialogVisible: false,\r\n      batchApprovalForm: {\r\n        action: '', // 'approve' 或 'reject'\r\n        rejectReason: ''\r\n      },\r\n    }\r\n  },\r\n  created() {\r\n    this.getTeam()\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    // 查询录单渠道、找车团队\r\n    getTeam() {\r\n      teamVm_car_order().then(response => {\r\n        this.teamList = response.team\r\n        this.jgNameList = response.office\r\n      })\r\n    },\r\n    /** 查询找车费用审批列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listVw_car_order_examine(this.queryParams).then(response => {\r\n        this.vw_car_order_examineList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: '',\r\n        status: 0,\r\n        newStatus: null,\r\n        rejectReason: null,\r\n        customerName: '',\r\n        customerId: '',\r\n        applyId: '',\r\n        plateNo: '',\r\n        jgName: '',\r\n        teamName: '',\r\n        allocationTime: '',\r\n        keyStatus: '',\r\n        totalMoney: '',\r\n        _readonly: false,\r\n      }\r\n      this.resetForm('form')\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      if (this.queryParams.originallyTime) {\r\n        this.queryParams.startTime = this.queryParams.originallyTime[0]\r\n        this.queryParams.endTime = this.queryParams.originallyTime[1]\r\n      }\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.queryParams.customerName = null\r\n      this.queryParams.plateNo = null\r\n      this.queryParams.jgName = null\r\n      this.queryParams.keyStatus = null\r\n      this.queryParams.teamName = null\r\n      this.queryParams.originallyTime = null\r\n      this.queryParams.startTime = null\r\n      this.queryParams.endTime = null\r\n      this.queryParams.garageName = null\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = '添加找车费用审批'\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      // this.reset()\r\n      this.form.id = row.id\r\n      this.form.rejectReason = row.rejectReason\r\n      this.form.status = row.status\r\n      // const id = row.id || this.ids\r\n      // getVw_car_order_examine(id).then(response => {\r\n      //   // this.form = response.data\r\n\r\n      // })\r\n      this.open = true\r\n      this.title = '找车费用审批'\r\n    },\r\n\r\n    // 获取状态文本\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        0: '未审批',\r\n        1: '全部通过',\r\n        2: '已拒绝',\r\n        3: '贷后主管审批',\r\n        4: '总监审批',\r\n        5: '财务主管/总监抄送',\r\n        6: '总经理/董事长审批(抄送)'\r\n      }\r\n      return statusMap[status] || '未知状态'\r\n    },\r\n\r\n\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids\r\n      this.$modal\r\n        .confirm('是否确认删除找车费用审批编号为\"' + ids + '\"的数据项？')\r\n        .then(function () {\r\n          return delVw_car_order_examine(ids)\r\n        })\r\n        .then(() => {\r\n          this.getList()\r\n          this.$modal.msgSuccess('删除成功')\r\n        })\r\n        .catch(() => {})\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\r\n        'vw_car_order_examine/vw_car_order_examine/export',\r\n        {\r\n          ...this.queryParams,\r\n        },\r\n        `vw_car_order_examine_${new Date().getTime()}.xlsx`\r\n      )\r\n    },\r\n    openUserInfo(customerInfo) {\r\n      console.log('点击客户信息:', customerInfo)\r\n      if (!customerInfo.customerId || !customerInfo.applyId) {\r\n        this.$modal.msgError('客户信息不完整，无法查看详情')\r\n        return\r\n      }\r\n      this.customerInfo = customerInfo\r\n      this.userInfoVisible = true\r\n    },\r\n    openCarInfo(plateNo) {\r\n      this.plateNo = plateNo\r\n      this.carInfoVisible = true\r\n    },\r\n\r\n    /** 查看费用详情和审批 */\r\n    handleViewDetails(row) {\r\n      this.currentOrderInfo = row\r\n      this.detailsDialogVisible = true\r\n      this.loadFeeRecords(row.orderId)\r\n    },\r\n\r\n    /** 加载费用记录 */\r\n    loadFeeRecords(orderId) {\r\n      this.recordsLoading = true\r\n      getCarOrderExamineRecords(orderId).then(response => {\r\n        this.feeRecords = response.data || []\r\n        this.recordsLoading = false\r\n      }).catch(error => {\r\n        this.recordsLoading = false\r\n        console.error('加载费用记录失败:', error)\r\n        this.$modal.msgError('加载费用记录失败')\r\n      })\r\n    },\r\n\r\n    /** 费用记录选择变化 */\r\n    handleRecordSelectionChange(selection) {\r\n      this.selectedRecords = selection\r\n    },\r\n\r\n    /** 单个审批 */\r\n    handleSingleApprove(record, action) {\r\n      this.singleApprovalForm.id = record.id\r\n      this.singleApprovalForm.action = action\r\n      this.singleApprovalForm.rejectReason = ''\r\n      this.singleApprovalDialogVisible = true\r\n    },\r\n\r\n    /** 确认单个审批 */\r\n    confirmSingleApproval() {\r\n      if (this.singleApprovalForm.action === 'reject') {\r\n        this.$refs[\"singleApprovalForm\"].validate(valid => {\r\n          if (!valid) return\r\n          this.executeSingleApproval()\r\n        })\r\n      } else {\r\n        this.executeSingleApproval()\r\n      }\r\n    },\r\n\r\n    /** 执行单个审批 */\r\n    executeSingleApproval() {\r\n      const data = {\r\n        id: this.singleApprovalForm.id,\r\n        action: this.singleApprovalForm.action,\r\n        rejectReason: this.singleApprovalForm.rejectReason\r\n      }\r\n\r\n      singleApproveCarOrderExamine(data).then(() => {\r\n        this.$modal.msgSuccess(`${this.singleApprovalForm.action === 'approve' ? '通过' : '拒绝'}审批成功`)\r\n        this.singleApprovalDialogVisible = false\r\n        this.loadFeeRecords(this.currentOrderInfo.id)\r\n        this.getList()\r\n      }).catch((error) => {\r\n        // 不显示通用错误提示，因为 request.js 已经处理了错误显示\r\n        console.error('审批失败:', error)\r\n      })\r\n    },\r\n\r\n    /** 批量审批 */\r\n    handleBatchApprove(action) {\r\n      if (this.selectedRecords.length === 0) {\r\n        this.$modal.msgError('请选择要审批的记录')\r\n        return\r\n      }\r\n\r\n      this.batchApprovalForm.action = action\r\n      this.batchApprovalForm.rejectReason = ''\r\n      this.batchApprovalDialogVisible = true\r\n    },\r\n\r\n    /** 确认批量审批 */\r\n    confirmBatchApproval() {\r\n      if (this.batchApprovalForm.action === 'reject') {\r\n        this.$refs[\"batchApprovalForm\"].validate(valid => {\r\n          if (!valid) return\r\n          this.executeBatchApproval()\r\n        })\r\n      } else {\r\n        this.executeBatchApproval()\r\n      }\r\n    },\r\n\r\n    /** 执行批量审批 */\r\n    executeBatchApproval() {\r\n      const data = {\r\n        ids: this.selectedRecords.map(record => record.id),\r\n        action: this.batchApprovalForm.action,\r\n        rejectReason: this.batchApprovalForm.rejectReason\r\n      }\r\n\r\n      batchApproveCarOrderExamine(data).then(() => {\r\n        this.$modal.msgSuccess(`批量${this.batchApprovalForm.action === 'approve' ? '通过' : '拒绝'}审批成功`)\r\n        this.batchApprovalDialogVisible = false\r\n        this.loadFeeRecords(this.currentOrderInfo.id)\r\n        this.getList()\r\n      }).catch((error) => {\r\n        // 不显示通用错误提示，因为 request.js 已经处理了错误显示\r\n        console.error('批量审批失败:', error)\r\n      })\r\n    },\r\n\r\n    /** 检查记录是否可以审批 */\r\n    canApproveRecord(record) {\r\n      // 只有未审批的记录可以审批\r\n      return record.status === 0 || record.status === null || record.status === ''\r\n    },\r\n\r\n    /** 获取状态标签类型 */\r\n    getStatusTagType(status) {\r\n      switch (status) {\r\n        case 0:\r\n          return 'info'\r\n        case 1:\r\n          return 'success'\r\n        case 7:\r\n          return 'danger'\r\n        case 3:\r\n        case 4:\r\n        case 5:\r\n        case 6:\r\n          return 'warning'\r\n        default:\r\n          return 'info'\r\n      }\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.approval-header {\r\n  background-color: #f5f7fa;\r\n  padding: 15px;\r\n  border-radius: 4px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.batch-approval-section {\r\n  border: 1px solid #e4e7ed;\r\n  padding: 10px;\r\n  border-radius: 4px;\r\n  background-color: #fafafa;\r\n}\r\n\r\n.el-table {\r\n  margin-top: 10px;\r\n}\r\n\r\n.el-tag {\r\n  margin: 2px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;AAuRA,IAAAA,qBAAA,GAAAC,OAAA;AAQA,IAAAC,SAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,QAAA,GAAAD,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAI,IAAA;EACAC,UAAA;IACAC,QAAA,EAAAA,iBAAA;IACAC,OAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,wBAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA;QACAC,SAAA;QACAC,cAAA;QACAC,SAAA;QACAC,OAAA;QACAC,YAAA;QACAC,OAAA;QACAC,MAAA;QACAC,UAAA;MACA;MACA;MACAC,IAAA;QACAC,EAAA;QACAC,MAAA;QACAC,SAAA;QACAC,YAAA;QACAR,YAAA;QACAS,UAAA;QACAC,OAAA;QACAT,OAAA;QACAC,MAAA;QACAP,QAAA;QACAgB,cAAA;QACAf,SAAA;QACAgB,UAAA;QACAC,SAAA;MACA;MACA;MACAC,KAAA;QACAT,EAAA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACAC,UAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAC,aAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAE,QAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAG,YAAA;QAAAd,UAAA;QAAAC,OAAA;MAAA;MACAc,eAAA;MACAvB,OAAA;MACAwB,cAAA;MACA;MACAC,oBAAA;MACA;MACAC,gBAAA;MACA;MACAC,UAAA;MACA;MACAC,cAAA;MACA;MACAC,eAAA;MACA;MACAC,2BAAA;MACAC,kBAAA;QACA3B,EAAA;QACA4B,MAAA;QAAA;QACAzB,YAAA;MACA;MACA;MACA0B,0BAAA;MACAC,iBAAA;QACAF,MAAA;QAAA;QACAzB,YAAA;MACA;IACA;EACA;EACA4B,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA;IACAF,OAAA,WAAAA,QAAA;MAAA,IAAAG,KAAA;MACA,IAAAC,sCAAA,IAAAC,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAlB,QAAA,GAAAqB,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAtB,UAAA,GAAAyB,QAAA,CAAAE,MAAA;MACA;IACA;IACA,iBACAP,OAAA,WAAAA,QAAA;MAAA,IAAAQ,MAAA;MACA,KAAA/D,OAAA;MACA,IAAAgE,8CAAA,OAAAvD,WAAA,EAAAkD,IAAA,WAAAC,QAAA;QACAG,MAAA,CAAAzD,wBAAA,GAAAsD,QAAA,CAAAK,IAAA;QACAF,MAAA,CAAA1D,KAAA,GAAAuD,QAAA,CAAAvD,KAAA;QACA0D,MAAA,CAAA/D,OAAA;MACA;IACA;IACA;IACAkE,MAAA,WAAAA,OAAA;MACA,KAAA1D,IAAA;MACA,KAAA2D,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAA9C,IAAA;QACAC,EAAA;QACAC,MAAA;QACAC,SAAA;QACAC,YAAA;QACAR,YAAA;QACAS,UAAA;QACAC,OAAA;QACAT,OAAA;QACAC,MAAA;QACAP,QAAA;QACAgB,cAAA;QACAf,SAAA;QACAgB,UAAA;QACAC,SAAA;MACA;MACA,KAAAsC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,SAAA5D,WAAA,CAAAK,cAAA;QACA,KAAAL,WAAA,CAAAM,SAAA,QAAAN,WAAA,CAAAK,cAAA;QACA,KAAAL,WAAA,CAAAO,OAAA,QAAAP,WAAA,CAAAK,cAAA;MACA;MACA,KAAAL,WAAA,CAAAC,OAAA;MACA,KAAA6C,OAAA;IACA;IACA,aACAe,UAAA,WAAAA,WAAA;MACA,KAAA7D,WAAA,CAAAQ,YAAA;MACA,KAAAR,WAAA,CAAAS,OAAA;MACA,KAAAT,WAAA,CAAAU,MAAA;MACA,KAAAV,WAAA,CAAAI,SAAA;MACA,KAAAJ,WAAA,CAAAG,QAAA;MACA,KAAAH,WAAA,CAAAK,cAAA;MACA,KAAAL,WAAA,CAAAM,SAAA;MACA,KAAAN,WAAA,CAAAO,OAAA;MACA,KAAAP,WAAA,CAAAW,UAAA;MACA,KAAAiD,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAvE,GAAA,GAAAuE,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAApD,EAAA;MAAA;MACA,KAAApB,MAAA,GAAAsE,SAAA,CAAAG,MAAA;MACA,KAAAxE,QAAA,IAAAqE,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAT,KAAA;MACA,KAAA3D,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAsE,YAAA,WAAAA,aAAAC,GAAA;MACA;MACA,KAAAzD,IAAA,CAAAC,EAAA,GAAAwD,GAAA,CAAAxD,EAAA;MACA,KAAAD,IAAA,CAAAI,YAAA,GAAAqD,GAAA,CAAArD,YAAA;MACA,KAAAJ,IAAA,CAAAE,MAAA,GAAAuD,GAAA,CAAAvD,MAAA;MACA;MACA;MACA;;MAEA;MACA,KAAAf,IAAA;MACA,KAAAD,KAAA;IACA;IAEA;IACAwE,aAAA,WAAAA,cAAAxD,MAAA;MACA,IAAAyD,SAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAzD,MAAA;IACA;IAGA,aACA0D,YAAA,WAAAA,aAAAH,GAAA;MAAA,IAAAI,MAAA;MACA,IAAAjF,GAAA,GAAA6E,GAAA,CAAAxD,EAAA,SAAArB,GAAA;MACA,KAAAkF,MAAA,CACAC,OAAA,sBAAAnF,GAAA,aACA0D,IAAA;QACA,WAAA0B,6CAAA,EAAApF,GAAA;MACA,GACA0D,IAAA;QACAuB,MAAA,CAAA3B,OAAA;QACA2B,MAAA,CAAAC,MAAA,CAAAG,UAAA;MACA,GACAC,KAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,CACA,wDAAAC,cAAA,CAAAC,OAAA,MAEA,KAAAlF,WAAA,2BAAAmF,MAAA,CAEA,IAAAC,IAAA,GAAAC,OAAA,YACA;IACA;IACAC,YAAA,WAAAA,aAAAvD,YAAA;MACAwD,OAAA,CAAAC,GAAA,YAAAzD,YAAA;MACA,KAAAA,YAAA,CAAAd,UAAA,KAAAc,YAAA,CAAAb,OAAA;QACA,KAAAwD,MAAA,CAAAe,QAAA;QACA;MACA;MACA,KAAA1D,YAAA,GAAAA,YAAA;MACA,KAAAC,eAAA;IACA;IACA0D,WAAA,WAAAA,YAAAjF,OAAA;MACA,KAAAA,OAAA,GAAAA,OAAA;MACA,KAAAwB,cAAA;IACA;IAEA,gBACA0D,iBAAA,WAAAA,kBAAAtB,GAAA;MACA,KAAAlC,gBAAA,GAAAkC,GAAA;MACA,KAAAnC,oBAAA;MACA,KAAA0D,cAAA,CAAAvB,GAAA,CAAAwB,OAAA;IACA;IAEA,aACAD,cAAA,WAAAA,eAAAC,OAAA;MAAA,IAAAC,MAAA;MACA,KAAAzD,cAAA;MACA,IAAA0D,+CAAA,EAAAF,OAAA,EAAA3C,IAAA,WAAAC,QAAA;QACA2C,MAAA,CAAA1D,UAAA,GAAAe,QAAA,CAAA7D,IAAA;QACAwG,MAAA,CAAAzD,cAAA;MACA,GAAAyC,KAAA,WAAAkB,KAAA;QACAF,MAAA,CAAAzD,cAAA;QACAkD,OAAA,CAAAS,KAAA,cAAAA,KAAA;QACAF,MAAA,CAAApB,MAAA,CAAAe,QAAA;MACA;IACA;IAEA,eACAQ,2BAAA,WAAAA,4BAAAlC,SAAA;MACA,KAAAzB,eAAA,GAAAyB,SAAA;IACA;IAEA,WACAmC,mBAAA,WAAAA,oBAAAC,MAAA,EAAA1D,MAAA;MACA,KAAAD,kBAAA,CAAA3B,EAAA,GAAAsF,MAAA,CAAAtF,EAAA;MACA,KAAA2B,kBAAA,CAAAC,MAAA,GAAAA,MAAA;MACA,KAAAD,kBAAA,CAAAxB,YAAA;MACA,KAAAuB,2BAAA;IACA;IAEA,aACA6D,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MACA,SAAA7D,kBAAA,CAAAC,MAAA;QACA,KAAA6D,KAAA,uBAAAC,QAAA,WAAAC,KAAA;UACA,KAAAA,KAAA;UACAH,MAAA,CAAAI,qBAAA;QACA;MACA;QACA,KAAAA,qBAAA;MACA;IACA;IAEA,aACAA,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MACA,IAAApH,IAAA;QACAuB,EAAA,OAAA2B,kBAAA,CAAA3B,EAAA;QACA4B,MAAA,OAAAD,kBAAA,CAAAC,MAAA;QACAzB,YAAA,OAAAwB,kBAAA,CAAAxB;MACA;MAEA,IAAA2F,kDAAA,EAAArH,IAAA,EAAA4D,IAAA;QACAwD,MAAA,CAAAhC,MAAA,CAAAG,UAAA,IAAAM,MAAA,CAAAuB,MAAA,CAAAlE,kBAAA,CAAAC,MAAA;QACAiE,MAAA,CAAAnE,2BAAA;QACAmE,MAAA,CAAAd,cAAA,CAAAc,MAAA,CAAAvE,gBAAA,CAAAtB,EAAA;QACA6F,MAAA,CAAA5D,OAAA;MACA,GAAAgC,KAAA,WAAAkB,KAAA;QACA;QACAT,OAAA,CAAAS,KAAA,UAAAA,KAAA;MACA;IACA;IAEA,WACAY,kBAAA,WAAAA,mBAAAnE,MAAA;MACA,SAAAH,eAAA,CAAA4B,MAAA;QACA,KAAAQ,MAAA,CAAAe,QAAA;QACA;MACA;MAEA,KAAA9C,iBAAA,CAAAF,MAAA,GAAAA,MAAA;MACA,KAAAE,iBAAA,CAAA3B,YAAA;MACA,KAAA0B,0BAAA;IACA;IAEA,aACAmE,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA,SAAAnE,iBAAA,CAAAF,MAAA;QACA,KAAA6D,KAAA,sBAAAC,QAAA,WAAAC,KAAA;UACA,KAAAA,KAAA;UACAM,MAAA,CAAAC,oBAAA;QACA;MACA;QACA,KAAAA,oBAAA;MACA;IACA;IAEA,aACAA,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA,IAAA1H,IAAA;QACAE,GAAA,OAAA8C,eAAA,CAAA0B,GAAA,WAAAmC,MAAA;UAAA,OAAAA,MAAA,CAAAtF,EAAA;QAAA;QACA4B,MAAA,OAAAE,iBAAA,CAAAF,MAAA;QACAzB,YAAA,OAAA2B,iBAAA,CAAA3B;MACA;MAEA,IAAAiG,iDAAA,EAAA3H,IAAA,EAAA4D,IAAA;QACA8D,MAAA,CAAAtC,MAAA,CAAAG,UAAA,gBAAAM,MAAA,CAAA6B,MAAA,CAAArE,iBAAA,CAAAF,MAAA;QACAuE,MAAA,CAAAtE,0BAAA;QACAsE,MAAA,CAAApB,cAAA,CAAAoB,MAAA,CAAA7E,gBAAA,CAAAtB,EAAA;QACAmG,MAAA,CAAAlE,OAAA;MACA,GAAAgC,KAAA,WAAAkB,KAAA;QACA;QACAT,OAAA,CAAAS,KAAA,YAAAA,KAAA;MACA;IACA;IAEA,iBACAkB,gBAAA,WAAAA,iBAAAf,MAAA;MACA;MACA,OAAAA,MAAA,CAAArF,MAAA,UAAAqF,MAAA,CAAArF,MAAA,aAAAqF,MAAA,CAAArF,MAAA;IACA;IAEA,eACAqG,gBAAA,WAAAA,iBAAArG,MAAA;MACA,QAAAA,MAAA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;QACA;QACA;QACA;UACA;QACA;UACA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}