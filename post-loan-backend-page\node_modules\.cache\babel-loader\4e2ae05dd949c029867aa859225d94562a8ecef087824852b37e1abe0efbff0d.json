{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.checkPermi = checkPermi;\nexports.checkRole = checkRole;\nrequire(\"core-js/modules/es.array.includes.js\");\nrequire(\"core-js/modules/es.object.to-string.js\");\nrequire(\"core-js/modules/es.string.includes.js\");\nrequire(\"core-js/modules/esnext.iterator.constructor.js\");\nrequire(\"core-js/modules/esnext.iterator.some.js\");\nvar _store = _interopRequireDefault(require(\"@/store\"));\n/**\r\n * 字符权限校验\r\n * @param {Array} value 校验值\r\n * @returns {Boolean}\r\n */\nfunction checkPermi(value) {\n  if (value && value instanceof Array && value.length > 0) {\n    var permissions = _store.default.getters && _store.default.getters.permissions;\n    var permissionDatas = value;\n    var all_permission = \"*:*:*\";\n    var hasPermission = permissions.some(function (permission) {\n      return all_permission === permission || permissionDatas.includes(permission);\n    });\n    return hasPermission;\n  } else {\n    console.error(\"need roles! Like checkPermi=\\\"['system:user:add','system:user:edit']\\\"\");\n    return false;\n  }\n}\n\n/**\r\n * 角色权限校验\r\n * @param {Array} value 校验值\r\n * @returns {Boolean}\r\n */\nfunction checkRole(value) {\n  if (value && value instanceof Array && value.length > 0) {\n    var roles = _store.default.getters && _store.default.getters.roles;\n    var permissionRoles = value;\n    var super_admin = \"admin\";\n    var hasRole = roles.some(function (role) {\n      return super_admin === role || permissionRoles.includes(role);\n    });\n    return hasRole;\n  } else {\n    console.error(\"need roles! Like checkRole=\\\"['admin','editor']\\\"\");\n    return false;\n  }\n}", "map": {"version": 3, "names": ["_store", "_interopRequireDefault", "require", "check<PERSON><PERSON><PERSON>", "value", "Array", "length", "permissions", "store", "getters", "permissionDatas", "all_permission", "hasPermission", "some", "permission", "includes", "console", "error", "checkRole", "roles", "permissionRoles", "super_admin", "hasRole", "role"], "sources": ["D:/code_project/java_project/loan/post-loan-backend-page/src/utils/permission.js"], "sourcesContent": ["import store from '@/store'\r\n\r\n/**\r\n * 字符权限校验\r\n * @param {Array} value 校验值\r\n * @returns {Boolean}\r\n */\r\nexport function checkPermi(value) {\r\n  if (value && value instanceof Array && value.length > 0) {\r\n    const permissions = store.getters && store.getters.permissions\r\n    const permissionDatas = value\r\n    const all_permission = \"*:*:*\"\r\n\r\n    const hasPermission = permissions.some(permission => {\r\n      return all_permission === permission || permissionDatas.includes(permission)\r\n    })\r\n\r\n    return hasPermission\r\n\r\n  } else {\r\n    console.error(`need roles! Like checkPermi=\"['system:user:add','system:user:edit']\"`)\r\n    return false\r\n  }\r\n}\r\n\r\n/**\r\n * 角色权限校验\r\n * @param {Array} value 校验值\r\n * @returns {Boolean}\r\n */\r\nexport function checkRole(value) {\r\n  if (value && value instanceof Array && value.length > 0) {\r\n    const roles = store.getters && store.getters.roles\r\n    const permissionRoles = value\r\n    const super_admin = \"admin\"\r\n\r\n    const hasRole = roles.some(role => {\r\n      return super_admin === role || permissionRoles.includes(role)\r\n    })\r\n\r\n    return hasRole\r\n\r\n  } else {\r\n    console.error(`need roles! Like checkRole=\"['admin','editor']\"`)\r\n    return false\r\n  }\r\n}"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACA;AACA;AACA;AACA;AACO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAChC,IAAIA,KAAK,IAAIA,KAAK,YAAYC,KAAK,IAAID,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;IACvD,IAAMC,WAAW,GAAGC,cAAK,CAACC,OAAO,IAAID,cAAK,CAACC,OAAO,CAACF,WAAW;IAC9D,IAAMG,eAAe,GAAGN,KAAK;IAC7B,IAAMO,cAAc,GAAG,OAAO;IAE9B,IAAMC,aAAa,GAAGL,WAAW,CAACM,IAAI,CAAC,UAAAC,UAAU,EAAI;MACnD,OAAOH,cAAc,KAAKG,UAAU,IAAIJ,eAAe,CAACK,QAAQ,CAACD,UAAU,CAAC;IAC9E,CAAC,CAAC;IAEF,OAAOF,aAAa;EAEtB,CAAC,MAAM;IACLI,OAAO,CAACC,KAAK,yEAAuE,CAAC;IACrF,OAAO,KAAK;EACd;AACF;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASC,SAASA,CAACd,KAAK,EAAE;EAC/B,IAAIA,KAAK,IAAIA,KAAK,YAAYC,KAAK,IAAID,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;IACvD,IAAMa,KAAK,GAAGX,cAAK,CAACC,OAAO,IAAID,cAAK,CAACC,OAAO,CAACU,KAAK;IAClD,IAAMC,eAAe,GAAGhB,KAAK;IAC7B,IAAMiB,WAAW,GAAG,OAAO;IAE3B,IAAMC,OAAO,GAAGH,KAAK,CAACN,IAAI,CAAC,UAAAU,IAAI,EAAI;MACjC,OAAOF,WAAW,KAAKE,IAAI,IAAIH,eAAe,CAACL,QAAQ,CAACQ,IAAI,CAAC;IAC/D,CAAC,CAAC;IAEF,OAAOD,OAAO;EAEhB,CAAC,MAAM;IACLN,OAAO,CAACC,KAAK,oDAAkD,CAAC;IAChE,OAAO,KAAK;EACd;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}