package com.ruoyi.litigation_cost_approval.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Date;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.litigation_cost_approval.domain.LitigationCostApproval;
import com.ruoyi.litigation_cost_approval.domain.dto.SingleApprovalRequest;
import com.ruoyi.litigation_cost_approval.domain.dto.BatchApprovalRequest;
import com.ruoyi.litigation_cost_approval.service.ILitigationCostApprovalService;
import com.ruoyi.litigation_cost_approval.utils.LitigationApprovalStatusManager;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.service.ISysUserService;

/**
 * 法诉费用审批Controller
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@RestController
@RequestMapping("/litigation_cost_approval/litigation_cost_approval")
public class LitigationCostApprovalController extends BaseController
{
    @Autowired
    private ILitigationCostApprovalService litigationCostApprovalService;

    @Autowired
    private ISysUserService sysUserService;

    /**
     * 查询法诉费用审批列表（显示法诉记录，每个费用取最新的展示）
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:list')")
    @GetMapping("/list")
    public TableDataInfo list(LitigationCostApproval litigationCostApproval)
    {
        startPage();
        List<LitigationCostApproval> list = litigationCostApprovalService.selectLitigationCostApprovalList(litigationCostApproval);
        return getDataTable(list);
    }

    /**
     * 导出法诉费用审批列表
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:export')")
    @Log(title = "法诉费用审批", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LitigationCostApproval litigationCostApproval)
    {
        List<LitigationCostApproval> list = litigationCostApprovalService.selectLitigationCostApprovalList(litigationCostApproval);
        ExcelUtil<LitigationCostApproval> util = new ExcelUtil<LitigationCostApproval>(LitigationCostApproval.class);
        util.exportExcel(response, list, "法诉费用审批数据");
    }

    /**
     * 获取法诉费用审批详细信息
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(litigationCostApprovalService.selectLitigationCostApprovalById(id));
    }

    /**
     * 根据法诉案件ID获取费用提交记录详情（用于审批弹窗）
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:query')")
    @GetMapping("/records/{litigationCaseId}")
    public AjaxResult getSubmissionRecords(@PathVariable("litigationCaseId") Long litigationCaseId)
    {
        List<LitigationCostApproval> records = litigationCostApprovalService.selectSubmissionRecordsByLitigationCaseId(litigationCaseId);
        return success(records);
    }

    /**
     * 新增法诉费用审批
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:add')")
    @Log(title = "法诉费用审批", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LitigationCostApproval litigationCostApproval)
    {
        return toAjax(litigationCostApprovalService.insertLitigationCostApproval(litigationCostApproval));
    }

    /**
     * 修改法诉费用审批
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:edit')")
    @Log(title = "法诉费用审批", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LitigationCostApproval litigationCostApproval)
    {
        return toAjax(litigationCostApprovalService.updateLitigationCostApproval(litigationCostApproval));
    }

    /**
     * 删除法诉费用审批
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:remove')")
    @Log(title = "法诉费用审批", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(litigationCostApprovalService.deleteLitigationCostApprovalByIds(ids));
    }

    /**
     * 单个审批费用记录
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:approve')")
    @Log(title = "法诉费用审批", businessType = BusinessType.UPDATE)
    @PutMapping("/approve")
    public AjaxResult approve(@RequestBody SingleApprovalRequest request)
    {
        int result = litigationCostApprovalService.approveLitigationCostRecord(
            request.getId(), request.getStatus(), request.getRejectReason());
        return toAjax(result);
    }

    /**
     * 批量审批费用记录
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:approve')")
    @Log(title = "法诉费用审批", businessType = BusinessType.UPDATE)
    @PutMapping("/batchApprove")
    public AjaxResult batchApprove(@RequestBody BatchApprovalRequest request)
    {
        int result = litigationCostApprovalService.batchApproveLitigationCostRecords(
            request.getIds(), request.getStatus(), request.getRejectReason());
        return toAjax(result);
    }

    /**
     * 获取审批状态统计
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:query')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics()
    {
        List<Map<String, Object>> statistics = litigationCostApprovalService.getApprovalStatistics();
        return success(statistics);
    }

    /**
     * 查询待审批记录数量
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:query')")
    @GetMapping("/pendingCount")
    public AjaxResult getPendingCount()
    {
        int count = litigationCostApprovalService.countPendingApproval();
        return success(count);
    }

    /**
     * 开始审批流程
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:approve')")
    @Log(title = "开始审批流程", businessType = BusinessType.UPDATE)
    @PostMapping("/startApproval/{id}")
    public AjaxResult startApprovalFlow(@PathVariable("id") Long id)
    {
        if (id == null) {
            return error("审批ID不能为空");
        }

        // 获取审批信息
        LitigationCostApproval approval = litigationCostApprovalService.selectLitigationCostApprovalById(id);
        if (approval == null) {
            return error("找不到对应的审批信息");
        }

        // 检查当前状态是否可以开始审批流程
        if (approval.getApprovalStatus() != null && !approval.getApprovalStatus().equals(LitigationApprovalStatusManager.STATUS_PENDING)) {
            return error("当前状态不允许开始审批流程，当前状态为【" + LitigationApprovalStatusManager.getStatusText(approval.getApprovalStatus()) + "】");
        }

        // 设置为第一个审批节点
        approval.setApprovalStatus(LitigationApprovalStatusManager.startApprovalFlow());

        return toAjax(litigationCostApprovalService.updateLitigationCostApproval(approval));
    }

    /**
     * 审批通过
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:approve')")
    @Log(title = "审批通过", businessType = BusinessType.UPDATE)
    @PostMapping("/approve")
    public AjaxResult approveRecord(@RequestBody Map<String, Object> params)
    {
        Object idObj = params.get("id");
        if (idObj == null) {
            return error("审批ID不能为空");
        }

        Long id;
        try {
            id = Long.valueOf(idObj.toString());
        } catch (NumberFormatException e) {
            return error("审批ID格式不正确");
        }

        String currentStatus = (String) params.get("currentStatus");

        // 获取当前用户角色
        String currentUser = getUsername();
        String userRole = sysUserService.selectUserRoleGroup(currentUser);

        // 获取审批信息
        LitigationCostApproval approval = litigationCostApprovalService.selectLitigationCostApprovalById(id);
        if (approval == null) {
            return error("找不到对应的审批信息");
        }

        // 验证当前状态（使用安全的比较方法）
        if (!java.util.Objects.equals(approval.getApprovalStatus(), currentStatus)) {
            return error("审批状态已发生变化，请刷新页面后重试");
        }

        // 检查是否可以审批
        if (!LitigationApprovalStatusManager.canUserApprove(approval.getApprovalStatus(), userRole)) {
            return error("您没有权限进行当前阶段的审批，当前状态为【" + LitigationApprovalStatusManager.getStatusText(approval.getApprovalStatus()) + "】，需要角色：" + LitigationApprovalStatusManager.getRequiredRole(approval.getApprovalStatus()));
        }

        // 获取下一个状态
        String nextStatus = LitigationApprovalStatusManager.handleApprove(approval.getApprovalStatus());
        approval.setApprovalStatus(nextStatus);
        approval.setApproveTime(new Date());
        approval.setApproveBy(currentUser);
        approval.setApproveRole(userRole);
        approval.setReasons(null); // 清除拒绝原因

        return toAjax(litigationCostApprovalService.updateLitigationCostApproval(approval));
    }

    /**
     * 审批拒绝
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:approve')")
    @Log(title = "审批拒绝", businessType = BusinessType.UPDATE)
    @PostMapping("/reject")
    public AjaxResult rejectRecord(@RequestBody Map<String, Object> params)
    {
        Object idObj = params.get("id");
        if (idObj == null) {
            return error("审批ID不能为空");
        }

        Long id;
        try {
            id = Long.valueOf(idObj.toString());
        } catch (NumberFormatException e) {
            return error("审批ID格式不正确");
        }

        String currentStatus = (String) params.get("currentStatus");
        String rejectReason = (String) params.get("rejectReason");

        if (rejectReason == null || rejectReason.trim().isEmpty()) {
            return error("拒绝原因不能为空");
        }

        // 获取当前用户角色
        String currentUser = getUsername();
        String userRole = sysUserService.selectUserRoleGroup(currentUser);

        // 获取审批信息
        LitigationCostApproval approval = litigationCostApprovalService.selectLitigationCostApprovalById(id);
        if (approval == null) {
            return error("找不到对应的审批信息");
        }

        // 验证当前状态（使用安全的比较方法）
        if (!java.util.Objects.equals(approval.getApprovalStatus(), currentStatus)) {
            return error("审批状态已发生变化，请刷新页面后重试");
        }

        // 检查是否可以审批
        if (!LitigationApprovalStatusManager.canUserApprove(approval.getApprovalStatus(), userRole)) {
            return error("您没有权限进行当前阶段的审批，当前状态为【" + LitigationApprovalStatusManager.getStatusText(approval.getApprovalStatus()) + "】，需要角色：" + LitigationApprovalStatusManager.getRequiredRole(approval.getApprovalStatus()));
        }

        // 设置为拒绝状态
        approval.setApprovalStatus(LitigationApprovalStatusManager.handleReject(approval.getApprovalStatus()));
        approval.setReasons(rejectReason);
        approval.setApproveTime(new Date());
        approval.setApproveBy(currentUser);
        approval.setApproveRole(userRole);

        return toAjax(litigationCostApprovalService.updateLitigationCostApproval(approval));
    }

    /**
     * 批量审批费用记录
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:approve')")
    @Log(title = "批量审批", businessType = BusinessType.UPDATE)
    @PostMapping("/batchApproveNew")
    public AjaxResult batchApproveNew(@RequestBody Map<String, Object> params)
    {
        @SuppressWarnings("unchecked")
        List<Object> idsObj = (List<Object>) params.get("ids");
        List<Long> ids = new ArrayList<>();
        if (idsObj != null) {
            for (Object idObj : idsObj) {
                if (idObj instanceof Number) {
                    ids.add(((Number) idObj).longValue());
                } else {
                    ids.add(Long.valueOf(idObj.toString()));
                }
            }
        }
        String action = (String) params.get("action");
        String rejectReason = (String) params.get("rejectReason");

        if (ids == null || ids.isEmpty()) {
            return error("请选择要审批的记录");
        }

        if (!"approve".equals(action) && !"reject".equals(action)) {
            return error("无效的审批操作");
        }

        if ("reject".equals(action) && (rejectReason == null || rejectReason.trim().isEmpty())) {
            return error("拒绝审批时必须填写拒绝原因");
        }

        // 获取当前用户信息
        String currentUser = getUsername();
        String userRole = sysUserService.selectUserRoleGroup(currentUser);

        int successCount = 0;
        int failCount = 0;
        StringBuilder errorMessages = new StringBuilder();

        for (Long id : ids) {
            try {
                LitigationCostApproval approval = litigationCostApprovalService.selectLitigationCostApprovalById(id);
                if (approval == null) {
                    failCount++;
                    errorMessages.append("ID为").append(id).append("的记录不存在；");
                    continue;
                }

                // 保存原始状态
                String originalStatus = approval.getApprovalStatus();

                // 检查是否可以审批
                if (!LitigationApprovalStatusManager.canUserApprove(originalStatus, userRole)) {
                    failCount++;
                    errorMessages.append("ID为").append(id).append("的记录当前状态【")
                        .append(LitigationApprovalStatusManager.getStatusText(originalStatus))
                        .append("】不允许角色【").append(userRole).append("】审批，需要角色【")
                        .append(LitigationApprovalStatusManager.getRequiredRole(originalStatus))
                        .append("】；");
                    continue;
                }

                // 再次查询最新状态，确保数据一致性
                LitigationCostApproval latestApproval = litigationCostApprovalService.selectLitigationCostApprovalById(id);
                if (latestApproval == null) {
                    failCount++;
                    errorMessages.append("ID为").append(id).append("的记录已被删除；");
                    continue;
                }

                // 检查状态是否发生变化（使用安全的比较方法）
                if (!java.util.Objects.equals(originalStatus, latestApproval.getApprovalStatus())) {
                    failCount++;
                    errorMessages.append("ID为").append(id).append("的记录状态已变化，原状态：")
                        .append(originalStatus).append("，当前状态：").append(latestApproval.getApprovalStatus()).append("；");
                    continue;
                }

                // 执行审批操作
                String newStatus;
                if ("approve".equals(action)) {
                    newStatus = LitigationApprovalStatusManager.handleApprove(originalStatus);
                } else {
                    newStatus = LitigationApprovalStatusManager.handleReject(originalStatus);
                    latestApproval.setReasons(rejectReason);
                }

                latestApproval.setApprovalStatus(newStatus);
                latestApproval.setApproveTime(new Date());
                latestApproval.setApproveBy(currentUser);
                latestApproval.setApproveRole(userRole);

                int updateResult = litigationCostApprovalService.updateLitigationCostApproval(latestApproval);
                if (updateResult <= 0) {
                    failCount++;
                    errorMessages.append("ID为").append(id).append("的记录更新失败；");
                    continue;
                }

                successCount++;

            } catch (Exception e) {
                failCount++;
                errorMessages.append("ID为").append(id).append("的记录审批失败：").append(e.getMessage()).append("；");
            }
        }

        if (failCount == 0) {
            return success("批量审批成功，共处理 " + successCount + " 条记录");
        } else if (successCount == 0) {
            return error("批量审批失败：" + errorMessages.toString());
        } else {
            return success("批量审批完成，成功 " + successCount + " 条，失败 " + failCount + " 条。失败原因：" + errorMessages.toString());
        }
    }

    /**
     * 单个审批费用记录
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:approve')")
    @Log(title = "单个审批", businessType = BusinessType.UPDATE)
    @PostMapping("/singleApproveNew")
    public AjaxResult singleApproveNew(@RequestBody Map<String, Object> params)
    {
        Object idObj = params.get("id");
        if (idObj == null) {
            return error("审批ID不能为空");
        }

        Long id;
        try {
            id = Long.valueOf(idObj.toString());
        } catch (NumberFormatException e) {
            return error("审批ID格式不正确");
        }

        String action = (String) params.get("action");
        String rejectReason = (String) params.get("rejectReason");

        if (!"approve".equals(action) && !"reject".equals(action)) {
            return error("无效的审批操作");
        }

        if ("reject".equals(action) && (rejectReason == null || rejectReason.trim().isEmpty())) {
            return error("拒绝审批时必须填写拒绝原因");
        }

        // 获取当前用户信息
        String currentUser = getUsername();
        String userRole = sysUserService.selectUserRoleGroup(currentUser);

        try {
            LitigationCostApproval approval = litigationCostApprovalService.selectLitigationCostApprovalById(id);
            if (approval == null) {
                return error("找不到对应的审批记录");
            }

            // 保存原始状态用于后续检查
            String originalStatus = approval.getApprovalStatus();

            // 检查是否可以审批
            if (!LitigationApprovalStatusManager.canUserApprove(originalStatus, userRole)) {
                return error("您没有权限进行当前阶段的审批，当前状态为【" + LitigationApprovalStatusManager.getStatusText(originalStatus) + "】，需要角色：" + LitigationApprovalStatusManager.getRequiredRole(originalStatus) + "，您的角色：" + userRole);
            }

            // 再次查询最新状态，确保数据一致性
            LitigationCostApproval latestApproval = litigationCostApprovalService.selectLitigationCostApprovalById(id);
            if (latestApproval == null) {
                return error("审批记录已被删除");
            }

            // 检查状态是否发生变化（使用安全的比较方法）
            if (!java.util.Objects.equals(originalStatus, latestApproval.getApprovalStatus())) {
                return error("审批状态已发生变化，请刷新页面后重试。原状态：" + originalStatus + "，当前状态：" + latestApproval.getApprovalStatus());
            }

            // 执行审批操作
            String newStatus;
            if ("approve".equals(action)) {
                newStatus = LitigationApprovalStatusManager.handleApprove(originalStatus);
            } else {
                newStatus = LitigationApprovalStatusManager.handleReject(originalStatus);
                latestApproval.setReasons(rejectReason);
            }

            latestApproval.setApprovalStatus(newStatus);
            latestApproval.setApproveTime(new Date());
            latestApproval.setApproveBy(currentUser);
            latestApproval.setApproveRole(userRole);

            int updateResult = litigationCostApprovalService.updateLitigationCostApproval(latestApproval);
            if (updateResult <= 0) {
                return error("审批失败，数据更新异常");
            }

            return success("审批成功");

        } catch (Exception e) {
            return error("审批失败：" + e.getMessage());
        }
    }
}
