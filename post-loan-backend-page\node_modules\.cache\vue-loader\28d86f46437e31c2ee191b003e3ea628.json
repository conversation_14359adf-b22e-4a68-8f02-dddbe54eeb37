{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\vw_car_order_examine\\vw_car_order_examine\\index.vue?vue&type=style&index=0&id=6e8f48b0&scoped=true&lang=css", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\vw_car_order_examine\\vw_car_order_examine\\index.vue", "mtime": 1754297359817}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753353053523}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753353054636}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753353053916}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5hcHByb3ZhbC1oZWFkZXIgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOw0KICBwYWRkaW5nOiAxNXB4Ow0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQp9DQoNCi5iYXRjaC1hcHByb3ZhbC1zZWN0aW9uIHsNCiAgYm9yZGVyOiAxcHggc29saWQgI2U0ZTdlZDsNCiAgcGFkZGluZzogMTBweDsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmFmYWZhOw0KfQ0KDQouZWwtdGFibGUgew0KICBtYXJnaW4tdG9wOiAxMHB4Ow0KfQ0KDQouZWwtdGFnIHsNCiAgbWFyZ2luOiAycHg7DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8rBA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/vw_car_order_examine/vw_car_order_examine", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"\" prop=\"customerName\">\r\n        <el-input v-model=\"queryParams.customerName\" placeholder=\"贷款人账户、姓名\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"plateNo\">\r\n        <el-input v-model=\"queryParams.plateNo\" placeholder=\"请输入车牌号\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"jgName\">\r\n        <el-input v-model=\"queryParams.jgName\" placeholder=\"录入渠道名称\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"garageName\">\r\n        <el-input v-model=\"queryParams.garageName\" placeholder=\"请输入车库名称\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"keyStatus\">\r\n        <el-select v-model=\"queryParams.keyStatus\" placeholder=\"请选择钥匙状态\" clearable>\r\n          <el-option v-for=\"dict in keyStatusList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"teamName\">\r\n        <el-input v-model=\"queryParams.teamName\" placeholder=\"找车团队\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"派单时间\">\r\n        <el-date-picker\r\n          v-model=\"queryParams.originallyTime\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"></el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"vw_car_order_examineList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"序号\" align=\"center\" type=\"index\" width=\"55\" fixed=\"left\" />\r\n      <el-table-column label=\"贷款人\" align=\"center\" prop=\"customerName\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            v-if=\"scope.row.customerId && scope.row.applyId\"\r\n            type=\"text\"\r\n            @click=\"openUserInfo({ customerId: scope.row.customerId, applyId: scope.row.applyId })\">\r\n            {{ scope.row.customerName }}\r\n          </el-button>\r\n          <span v-else>{{ scope.row.customerName }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"联系电话\" align=\"center\" prop=\"mobilePhone\" />\r\n      <!-- 出单渠道 -->\r\n      <el-table-column label=\"出单渠道\" align=\"center\" prop=\"jgName\"></el-table-column>\r\n      <el-table-column label=\"车牌号\" align=\"center\" prop=\"plateNo\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button type=\"text\" @click=\"openCarInfo(scope.row.plateNo)\">{{ scope.row.plateNo }}</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"接单团队\" align=\"center\" prop=\"teamName\" />\r\n      <el-table-column label=\"派单时间\" align=\"center\" prop=\"allocationTime\" width=\"180\"></el-table-column>\r\n      <el-table-column label=\"钥匙状态\" align=\"center\" prop=\"keyStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ scope.row.keyStatus == 1 ? '已邮寄' : scope.row.keyStatus == 2 ? '已收回' : '未归还' }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"入库时间\" align=\"center\" prop=\"inboundTime\" width=\"180\"></el-table-column>\r\n      <el-table-column label=\"找车佣金\" align=\"center\" prop=\"locatingCommission\" />\r\n      <el-table-column label=\"佣金\" align=\"center\" prop=\"transportationFee\" />\r\n      <el-table-column label=\"拖车费\" align=\"center\" prop=\"towingFee\" />\r\n      <el-table-column label=\"贴机费\" align=\"center\" prop=\"trackerInstallationFee\" />\r\n      <el-table-column label=\"其他报销\" align=\"center\" prop=\"otherReimbursement\" />\r\n      <el-table-column label=\"合计费用\" align=\"center\" prop=\"totalMoney\" />\r\n      <el-table-column label=\"审批状态\" align=\"center\" prop=\"status\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ getStatusText(scope.row.status) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" fixed=\"right\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            @click=\"handleViewDetails(scope.row)\"\r\n            v-hasPermi=\"['vw_car_order_examine:vw_car_order_examine:edit']\">\r\n            审批\r\n          </el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\" />\r\n\r\n    <!-- 费用详情审批对话框 -->\r\n    <el-dialog title=\"找车费用审批详情\" :visible.sync=\"detailsDialogVisible\" width=\"1200px\" append-to-body>\r\n      <!-- 订单基本信息头部 -->\r\n      <div class=\"approval-header\">\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <strong>贷款人：</strong>\r\n            <el-button\r\n              v-if=\"currentOrderInfo && currentOrderInfo.customerId && currentOrderInfo.applyId\"\r\n              type=\"text\"\r\n              @click=\"openUserInfo({ customerId: currentOrderInfo.customerId, applyId: currentOrderInfo.applyId })\"\r\n              style=\"color: #409EFF;\">\r\n              {{ currentOrderInfo.customerName }}\r\n            </el-button>\r\n            <span v-else>{{ currentOrderInfo ? currentOrderInfo.customerName : '' }}</span>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <strong>接单团队：</strong>{{ currentOrderInfo ? currentOrderInfo.teamName : '' }}\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <strong>车牌号：</strong>\r\n            <el-button\r\n              v-if=\"currentOrderInfo && currentOrderInfo.plateNo\"\r\n              type=\"text\"\r\n              @click=\"openCarInfo(currentOrderInfo.plateNo)\"\r\n              style=\"color: #409EFF;\">\r\n              {{ currentOrderInfo.plateNo }}\r\n            </el-button>\r\n            <span v-else>{{ currentOrderInfo ? currentOrderInfo.plateNo : '' }}</span>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row style=\"margin-top: 10px;\">\r\n          <el-col :span=\"8\">\r\n            <strong>出单渠道：</strong>{{ currentOrderInfo ? currentOrderInfo.jgName : '' }}\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <strong>派单时间：</strong>{{ currentOrderInfo ? currentOrderInfo.allocationTime : '' }}\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <strong>钥匙状态：</strong>\r\n            <span v-if=\"currentOrderInfo\">{{ currentOrderInfo.keyStatus == 1 ? '已邮寄' : currentOrderInfo.keyStatus == 2 ? '已收回' : '未归还' }}</span>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <!-- 批量操作区域 -->\r\n      <div class=\"batch-approval-section\" style=\"margin: 20px 0;\">\r\n        <el-button\r\n          type=\"success\"\r\n          size=\"small\"\r\n          :disabled=\"selectedRecords.length === 0\"\r\n          @click=\"handleBatchApprove('approve')\">\r\n          批量通过 ({{ selectedRecords.length }})\r\n        </el-button>\r\n        <el-button\r\n          type=\"danger\"\r\n          size=\"small\"\r\n          :disabled=\"selectedRecords.length === 0\"\r\n          @click=\"handleBatchApprove('reject')\">\r\n          批量拒绝 ({{ selectedRecords.length }})\r\n        </el-button>\r\n      </div>\r\n\r\n      <el-table\r\n        :data=\"feeRecords\"\r\n        @selection-change=\"handleRecordSelectionChange\"\r\n        v-loading=\"recordsLoading\">\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" fixed=\"left\" />\r\n        <el-table-column label=\"提交时间\" align=\"center\" prop=\"applicationTime\" width=\"150\" />\r\n        <el-table-column label=\"提交人\" align=\"center\" prop=\"applicationBy\" width=\"100\" />\r\n        <el-table-column label=\"佣金\" align=\"center\" prop=\"transportationFee\" width=\"80\" />\r\n        <el-table-column label=\"拖车费\" align=\"center\" prop=\"towingFee\" width=\"80\" />\r\n        <el-table-column label=\"贴机费\" align=\"center\" prop=\"trackerInstallationFee\" width=\"80\" />\r\n        <el-table-column label=\"其他报销\" align=\"center\" prop=\"otherReimbursement\" width=\"80\" />\r\n        <el-table-column label=\"合计费用\" align=\"center\" prop=\"totalMoney\" width=\"100\" />\r\n        <el-table-column label=\"审批状态\" align=\"center\" prop=\"status\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag v-if=\"scope.row.status == null || scope.row.status == 0\" type=\"info\">未审核</el-tag>\r\n            <el-tag v-else-if=\"scope.row.status == 1\" type=\"success\">已通过</el-tag>\r\n            <el-tag v-else-if=\"scope.row.status == 2\" type=\"danger\">已拒绝</el-tag>\r\n            <el-tag v-else type=\"warning\">{{ getStatusText(scope.row.status) }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"拒绝原因\" align=\"center\" prop=\"reasons\" width=\"150\" />\r\n        <el-table-column label=\"操作\" align=\"center\" width=\"150\" fixed=\"right\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              v-if=\"canApproveRecord(scope.row)\"\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"handleSingleApprove(scope.row, 'approve')\">\r\n              通过\r\n            </el-button>\r\n            <el-button\r\n              v-if=\"canApproveRecord(scope.row)\"\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"handleSingleApprove(scope.row, 'reject')\">\r\n              拒绝\r\n            </el-button>\r\n            <div v-else>\r\n              <!-- 已完成状态 -->\r\n              <el-tag v-if=\"scope.row.status == 1\" type=\"success\" size=\"mini\">已通过</el-tag>\r\n              <el-tag v-else-if=\"scope.row.status == 2\" type=\"danger\" size=\"mini\">已拒绝</el-tag>\r\n\r\n              <!-- 审批中但权限不足 -->\r\n              <div v-else-if=\"[0, 3, 4, 5, 6].includes(scope.row.status) || scope.row.status == null\">\r\n                <el-tag type=\"info\" size=\"mini\">{{ getStatusText(scope.row.status) }}</el-tag>\r\n                <div style=\"font-size: 11px; color: #999; margin-top: 2px;\">\r\n                  需要：{{ getRequiredRoleText(scope.row.status) }}\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 其他状态 -->\r\n              <el-tag v-else type=\"warning\" size=\"mini\">{{ getStatusText(scope.row.status) }}</el-tag>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"detailsDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 单个审批确认对话框 -->\r\n    <el-dialog title=\"审批确认\" :visible.sync=\"singleApprovalDialogVisible\" width=\"400px\" append-to-body>\r\n      <el-form ref=\"singleApprovalForm\" :model=\"singleApprovalForm\" label-width=\"80px\">\r\n        <el-form-item label=\"审批结果\">\r\n          <el-tag :type=\"singleApprovalForm.action === 'approve' ? 'success' : 'danger'\">\r\n            {{ singleApprovalForm.action === 'approve' ? '通过' : '拒绝' }}\r\n          </el-tag>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"拒绝原因\"\r\n          prop=\"rejectReason\"\r\n          v-if=\"singleApprovalForm.action === 'reject'\"\r\n          :rules=\"[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]\">\r\n          <el-input\r\n            v-model=\"singleApprovalForm.rejectReason\"\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入拒绝原因\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"confirmSingleApproval\">确 定</el-button>\r\n        <el-button @click=\"singleApprovalDialogVisible = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批量审批确认对话框 -->\r\n    <el-dialog title=\"批量审批确认\" :visible.sync=\"batchApprovalDialogVisible\" width=\"400px\" append-to-body>\r\n      <el-form ref=\"batchApprovalForm\" :model=\"batchApprovalForm\" label-width=\"80px\">\r\n        <el-form-item label=\"审批结果\">\r\n          <el-tag :type=\"batchApprovalForm.action === 'approve' ? 'success' : 'danger'\">\r\n            {{ batchApprovalForm.action === 'approve' ? '批量通过' : '批量拒绝' }}\r\n          </el-tag>\r\n        </el-form-item>\r\n        <el-form-item label=\"选中记录\">\r\n          <span>{{ selectedRecords.length }} 条记录</span>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"拒绝原因\"\r\n          prop=\"rejectReason\"\r\n          v-if=\"batchApprovalForm.action === 'reject'\"\r\n          :rules=\"[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]\">\r\n          <el-input\r\n            v-model=\"batchApprovalForm.rejectReason\"\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入拒绝原因\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"confirmBatchApproval\">确 定</el-button>\r\n        <el-button @click=\"batchApprovalDialogVisible = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 贷款人信息组件 -->\r\n    <userInfo ref=\"userInfo\" :visible.sync=\"userInfoVisible\" title=\"贷款人信息\" :customerInfo=\"customerInfo\" />\r\n    <!-- 车辆信息组件 -->\r\n    <carInfo ref=\"carInfo\" :visible.sync=\"carInfoVisible\" title=\"车辆信息\" :plateNo=\"plateNo\" permission=\"2\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  teamVm_car_order,\r\n  listVw_car_order_examine,\r\n  delVw_car_order_examine,\r\n  getCarOrderExamineRecords,\r\n  batchApproveCarOrderExamine,\r\n  singleApproveCarOrderExamine\r\n} from '@/api/vw_car_order_examine/vw_car_order_examine'\r\nimport userInfo from '@/layout/components/Dialog/userInfo.vue'\r\nimport carInfo from '@/layout/components/Dialog/carInfo.vue'\r\n\r\nexport default {\r\n  name: 'Vw_car_order_examine',\r\n  components: {\r\n    userInfo,\r\n    carInfo,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 找车费用审批表格数据\r\n      vw_car_order_examineList: [],\r\n      // 弹出层标题\r\n      title: '',\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 15,\r\n        teamName: null,\r\n        keyStatus: null,\r\n        originallyTime: null,\r\n        startTime: '',\r\n        endTime: '',\r\n        customerName: null,\r\n        plateNo: null,\r\n        jgName: null,\r\n        garageName: null,\r\n      },\r\n      // 表单参数\r\n      form: {\r\n        id: '',\r\n        status: 0,\r\n        newStatus: null,\r\n        rejectReason: null,\r\n        customerName: '',\r\n        customerId: '',\r\n        applyId: '',\r\n        plateNo: '',\r\n        jgName: '',\r\n        teamName: '',\r\n        allocationTime: '',\r\n        keyStatus: '',\r\n        totalMoney: '',\r\n        _readonly: false,\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        id: [{ required: true, message: '$comment不能为空', trigger: 'blur' }],\r\n      },\r\n      jgNameList: [\r\n        { label: 'A公司', value: 1 },\r\n        { label: 'B公司', value: 2 },\r\n      ],\r\n      keyStatusList: [\r\n        { label: '已邮寄', value: 1 },\r\n        { label: '已收回', value: 2 },\r\n        { label: '已归还', value: 3 },\r\n      ],\r\n      teamList: [\r\n        { label: 'A团队', value: 1 },\r\n        { label: 'B团队', value: 2 },\r\n      ],\r\n      customerInfo: { customerId: '', applyId: '' },\r\n      userInfoVisible: false,\r\n      plateNo: '',\r\n      carInfoVisible: false,\r\n      // 详情对话框显示状态\r\n      detailsDialogVisible: false,\r\n      // 当前订单信息\r\n      currentOrderInfo: null,\r\n      // 费用记录列表\r\n      feeRecords: [],\r\n      // 费用记录加载状态\r\n      recordsLoading: false,\r\n      // 选中的费用记录\r\n      selectedRecords: [],\r\n      // 单个审批对话框\r\n      singleApprovalDialogVisible: false,\r\n      singleApprovalForm: {\r\n        id: '',\r\n        action: '', // 'approve' 或 'reject'\r\n        rejectReason: ''\r\n      },\r\n      // 批量审批对话框\r\n      batchApprovalDialogVisible: false,\r\n      batchApprovalForm: {\r\n        action: '', // 'approve' 或 'reject'\r\n        rejectReason: ''\r\n      },\r\n    }\r\n  },\r\n  created() {\r\n    this.getTeam()\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    // 查询录单渠道、找车团队\r\n    getTeam() {\r\n      teamVm_car_order().then(response => {\r\n        this.teamList = response.team\r\n        this.jgNameList = response.office\r\n      })\r\n    },\r\n    /** 查询找车费用审批列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listVw_car_order_examine(this.queryParams).then(response => {\r\n        this.vw_car_order_examineList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: '',\r\n        status: 0,\r\n        newStatus: null,\r\n        rejectReason: null,\r\n        customerName: '',\r\n        customerId: '',\r\n        applyId: '',\r\n        plateNo: '',\r\n        jgName: '',\r\n        teamName: '',\r\n        allocationTime: '',\r\n        keyStatus: '',\r\n        totalMoney: '',\r\n        _readonly: false,\r\n      }\r\n      this.resetForm('form')\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      if (this.queryParams.originallyTime) {\r\n        this.queryParams.startTime = this.queryParams.originallyTime[0]\r\n        this.queryParams.endTime = this.queryParams.originallyTime[1]\r\n      }\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.queryParams.customerName = null\r\n      this.queryParams.plateNo = null\r\n      this.queryParams.jgName = null\r\n      this.queryParams.keyStatus = null\r\n      this.queryParams.teamName = null\r\n      this.queryParams.originallyTime = null\r\n      this.queryParams.startTime = null\r\n      this.queryParams.endTime = null\r\n      this.queryParams.garageName = null\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = '添加找车费用审批'\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      // this.reset()\r\n      this.form.id = row.id\r\n      this.form.rejectReason = row.rejectReason\r\n      this.form.status = row.status\r\n      // const id = row.id || this.ids\r\n      // getVw_car_order_examine(id).then(response => {\r\n      //   // this.form = response.data\r\n\r\n      // })\r\n      this.open = true\r\n      this.title = '找车费用审批'\r\n    },\r\n\r\n    // 获取状态文本\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        0: '未审批',\r\n        1: '全部通过',\r\n        2: '已拒绝',\r\n        3: '贷后主管审批',\r\n        4: '总监审批',\r\n        5: '财务主管/总监抄送',\r\n        6: '总经理/董事长审批(抄送)'\r\n      }\r\n      return statusMap[status] || '未知状态'\r\n    },\r\n\r\n    /** 获取当前状态需要的审批角色文本 */\r\n    getRequiredRoleText(status) {\r\n      switch (status) {\r\n        case 0: // 未审批\r\n        case null:\r\n        case undefined:\r\n          return '贷后主管'\r\n        case 3: // 贷后主管审批状态\r\n          return '总监'\r\n        case 4: // 总监审批状态\r\n          return '财务主管/总监'\r\n        case 5: // 财务主管/总监抄送状态\r\n          return '总经理/董事长'\r\n        case 6: // 总经理/董事长审批状态\r\n          return '已完成'\r\n        default:\r\n          return '未知'\r\n      }\r\n    },\r\n\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids\r\n      this.$modal\r\n        .confirm('是否确认删除找车费用审批编号为\"' + ids + '\"的数据项？')\r\n        .then(function () {\r\n          return delVw_car_order_examine(ids)\r\n        })\r\n        .then(() => {\r\n          this.getList()\r\n          this.$modal.msgSuccess('删除成功')\r\n        })\r\n        .catch(() => {})\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\r\n        'vw_car_order_examine/vw_car_order_examine/export',\r\n        {\r\n          ...this.queryParams,\r\n        },\r\n        `vw_car_order_examine_${new Date().getTime()}.xlsx`\r\n      )\r\n    },\r\n    openUserInfo(customerInfo) {\r\n      console.log('点击客户信息:', customerInfo)\r\n      if (!customerInfo.customerId || !customerInfo.applyId) {\r\n        this.$modal.msgError('客户信息不完整，无法查看详情')\r\n        return\r\n      }\r\n      this.customerInfo = customerInfo\r\n      this.userInfoVisible = true\r\n    },\r\n    openCarInfo(plateNo) {\r\n      this.plateNo = plateNo\r\n      this.carInfoVisible = true\r\n    },\r\n\r\n    /** 查看费用详情和审批 */\r\n    handleViewDetails(row) {\r\n      this.currentOrderInfo = row\r\n      this.detailsDialogVisible = true\r\n      this.loadFeeRecords(row.orderId)\r\n    },\r\n\r\n    /** 加载费用记录 */\r\n    loadFeeRecords(orderId) {\r\n      this.recordsLoading = true\r\n      getCarOrderExamineRecords(orderId).then(response => {\r\n        this.feeRecords = response.data || []\r\n        this.recordsLoading = false\r\n      }).catch(error => {\r\n        this.recordsLoading = false\r\n        console.error('加载费用记录失败:', error)\r\n        this.$modal.msgError('加载费用记录失败')\r\n      })\r\n    },\r\n\r\n    /** 费用记录选择变化 */\r\n    handleRecordSelectionChange(selection) {\r\n      this.selectedRecords = selection\r\n    },\r\n\r\n    /** 单个审批 */\r\n    handleSingleApprove(record, action) {\r\n      this.singleApprovalForm.id = record.id\r\n      this.singleApprovalForm.action = action\r\n      this.singleApprovalForm.rejectReason = ''\r\n      this.singleApprovalDialogVisible = true\r\n    },\r\n\r\n    /** 确认单个审批 */\r\n    confirmSingleApproval() {\r\n      if (this.singleApprovalForm.action === 'reject') {\r\n        this.$refs[\"singleApprovalForm\"].validate(valid => {\r\n          if (!valid) return\r\n          this.executeSingleApproval()\r\n        })\r\n      } else {\r\n        this.executeSingleApproval()\r\n      }\r\n    },\r\n\r\n    /** 执行单个审批 */\r\n    executeSingleApproval() {\r\n      const data = {\r\n        id: this.singleApprovalForm.id,\r\n        action: this.singleApprovalForm.action,\r\n        rejectReason: this.singleApprovalForm.rejectReason\r\n      }\r\n\r\n      singleApproveCarOrderExamine(data).then(() => {\r\n        this.$modal.msgSuccess(`${this.singleApprovalForm.action === 'approve' ? '通过' : '拒绝'}审批成功`)\r\n        this.singleApprovalDialogVisible = false\r\n        this.loadFeeRecords(this.currentOrderInfo.id)\r\n        this.getList()\r\n      }).catch((error) => {\r\n        // 不显示通用错误提示，因为 request.js 已经处理了错误显示\r\n        console.error('审批失败:', error)\r\n      })\r\n    },\r\n\r\n    /** 批量审批 */\r\n    handleBatchApprove(action) {\r\n      if (this.selectedRecords.length === 0) {\r\n        this.$modal.msgError('请选择要审批的记录')\r\n        return\r\n      }\r\n\r\n      this.batchApprovalForm.action = action\r\n      this.batchApprovalForm.rejectReason = ''\r\n      this.batchApprovalDialogVisible = true\r\n    },\r\n\r\n    /** 确认批量审批 */\r\n    confirmBatchApproval() {\r\n      if (this.batchApprovalForm.action === 'reject') {\r\n        this.$refs[\"batchApprovalForm\"].validate(valid => {\r\n          if (!valid) return\r\n          this.executeBatchApproval()\r\n        })\r\n      } else {\r\n        this.executeBatchApproval()\r\n      }\r\n    },\r\n\r\n    /** 执行批量审批 */\r\n    executeBatchApproval() {\r\n      const data = {\r\n        ids: this.selectedRecords.map(record => record.id),\r\n        action: this.batchApprovalForm.action,\r\n        rejectReason: this.batchApprovalForm.rejectReason\r\n      }\r\n\r\n      batchApproveCarOrderExamine(data).then(() => {\r\n        this.$modal.msgSuccess(`批量${this.batchApprovalForm.action === 'approve' ? '通过' : '拒绝'}审批成功`)\r\n        this.batchApprovalDialogVisible = false\r\n        this.loadFeeRecords(this.currentOrderInfo.id)\r\n        this.getList()\r\n      }).catch((error) => {\r\n        // 不显示通用错误提示，因为 request.js 已经处理了错误显示\r\n        console.error('批量审批失败:', error)\r\n      })\r\n    },\r\n\r\n    /** 检查记录是否可以审批 */\r\n    canApproveRecord(record) {\r\n      // 只有未审批的记录可以审批\r\n      return record.status === 0 || record.status === null || record.status === ''\r\n    },\r\n\r\n    /** 获取状态标签类型 */\r\n    getStatusTagType(status) {\r\n      switch (status) {\r\n        case 0:\r\n          return 'info'\r\n        case 1:\r\n          return 'success'\r\n        case 7:\r\n          return 'danger'\r\n        case 3:\r\n        case 4:\r\n        case 5:\r\n        case 6:\r\n          return 'warning'\r\n        default:\r\n          return 'info'\r\n      }\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.approval-header {\r\n  background-color: #f5f7fa;\r\n  padding: 15px;\r\n  border-radius: 4px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.batch-approval-section {\r\n  border: 1px solid #e4e7ed;\r\n  padding: 10px;\r\n  border-radius: 4px;\r\n  background-color: #fafafa;\r\n}\r\n\r\n.el-table {\r\n  margin-top: 10px;\r\n}\r\n\r\n.el-tag {\r\n  margin: 2px;\r\n}\r\n</style>\r\n"]}]}