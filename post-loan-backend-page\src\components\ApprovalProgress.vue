<template>
  <div class="approval-progress">
    <el-steps :active="currentStep" :status="stepStatus" align-center>
      <el-step
        v-for="(step, index) in steps"
        :key="index"
        :title="step.title"
        :description="step.description"
        :status="step.status"
      ></el-step>
    </el-steps>
    
    <div v-if="rejectReason" class="reject-reason">
      <el-alert
        title="拒绝原因"
        type="error"
        :description="rejectReason"
        show-icon
        :closable="false"
      ></el-alert>
    </div>
  </div>
</template>

<script>
import { APPROVAL_STATUS, APPROVAL_FLOW } from "@/utils/approvalStatus"
import { LITIGATION_APPROVAL_STATUS, LITIGATION_APPROVAL_FLOW } from "@/utils/litigationApprovalStatus"

export default {
  name: "ApprovalProgress",
  props: {
    status: {
      type: [Number, String], // 支持数字和字符串类型
      required: true
    },
    rejectReason: {
      type: String,
      default: ''
    },
    // 新增业务类型属性，用于区分不同的审批流程
    businessType: {
      type: String,
      default: 'car_order', // 默认为找车费用审批
      validator: function (value) {
        return ['car_order', 'litigation'].indexOf(value) !== -1
      }
    }
  },
  computed: {
    // 根据业务类型获取对应的状态常量和流程
    statusConstants() {
      return this.businessType === 'litigation' ? LITIGATION_APPROVAL_STATUS : APPROVAL_STATUS
    },
    approvalFlow() {
      return this.businessType === 'litigation' ? LITIGATION_APPROVAL_FLOW : APPROVAL_FLOW
    },
    steps() {
      // 根据业务类型返回不同的步骤配置
      if (this.businessType === 'litigation') {
        // 法诉费用审批流程
        const steps = [
          {
            title: '法诉主管审批',
            description: '法务诉讼主管审批',
            status: this.getStepStatus(LITIGATION_APPROVAL_STATUS.LEGAL_SUPERVISOR)
          },
          {
            title: '总监审批',
            description: '部门总监审批',
            status: this.getStepStatus(LITIGATION_APPROVAL_STATUS.DIRECTOR)
          },
          {
            title: '财务主管/总监抄送',
            description: '财务部门审批',
            status: this.getStepStatus(LITIGATION_APPROVAL_STATUS.FINANCE_SUPERVISOR)
          },
          {
            title: '总经理/董事长审批',
            description: '最高管理层审批',
            status: this.getStepStatus(LITIGATION_APPROVAL_STATUS.GENERAL_MANAGER)
          }
        ]
        return steps
      } else {
        // 找车费用审批流程（默认）
        const steps = [
          {
            title: '贷后主管审批',
            description: '贷后主管审批',
            status: this.getStepStatus(APPROVAL_STATUS.LEGAL_SUPERVISOR)
          },
          {
            title: '总监审批',
            description: '部门总监审批',
            status: this.getStepStatus(APPROVAL_STATUS.DIRECTOR)
          },
          {
            title: '财务主管/总监抄送',
            description: '财务部门审批',
            status: this.getStepStatus(APPROVAL_STATUS.FINANCE_SUPERVISOR)
          },
          {
            title: '总经理/董事长审批',
            description: '最高管理层审批',
            status: this.getStepStatus(APPROVAL_STATUS.GENERAL_MANAGER)
          }
        ]
        return steps
      }
    },
    currentStep() {
      const pendingStatus = this.statusConstants.PENDING
      const rejectedStatus = this.statusConstants.REJECTED
      const approvedStatus = this.statusConstants.APPROVED
      const flow = this.approvalFlow

      if (this.status === pendingStatus || this.status == pendingStatus) {
        return 0
      }
      if (this.status === rejectedStatus || this.status == rejectedStatus) {
        // 找到当前被拒绝的步骤
        const currentIndex = flow.indexOf(this.getCurrentApprovalNode())
        return currentIndex >= 0 ? currentIndex : 0
      }
      if (this.status === approvedStatus || this.status == approvedStatus) {
        return flow.length
      }

      const currentIndex = flow.indexOf(this.status)
      return currentIndex >= 0 ? currentIndex : 0
    },
    stepStatus() {
      const rejectedStatus = this.statusConstants.REJECTED
      const approvedStatus = this.statusConstants.APPROVED

      if (this.status === rejectedStatus || this.status == rejectedStatus) {
        return 'error'
      }
      if (this.status === approvedStatus || this.status == approvedStatus) {
        return 'success'
      }
      return 'process'
    }
  },
  methods: {
    getStepStatus(stepStatus) {
      const rejectedStatus = this.statusConstants.REJECTED
      const approvedStatus = this.statusConstants.APPROVED
      const flow = this.approvalFlow

      if (this.status === rejectedStatus || this.status == rejectedStatus) {
        const currentIndex = flow.indexOf(this.getCurrentApprovalNode())
        const stepIndex = flow.indexOf(stepStatus)
        if (stepIndex < currentIndex) {
          return 'success'
        } else if (stepIndex === currentIndex) {
          return 'error'
        } else {
          return 'wait'
        }
      }

      if (this.status === approvedStatus || this.status == approvedStatus) {
        return 'success'
      }

      const currentIndex = flow.indexOf(this.status)
      const stepIndex = flow.indexOf(stepStatus)

      if (stepIndex < currentIndex) {
        return 'success'
      } else if (stepIndex === currentIndex) {
        return 'process'
      } else {
        return 'wait'
      }
    },
    getCurrentApprovalNode() {
      // 这里需要根据实际情况确定当前被拒绝的节点
      // 可以通过额外的参数传入，或者从后端获取
      return this.approvalFlow[0] // 默认返回第一个节点
    }
  }
}
</script>

<style scoped>
.approval-progress {
  padding: 20px;
}

.reject-reason {
  margin-top: 20px;
}

.el-steps {
  margin-bottom: 20px;
}
</style>
